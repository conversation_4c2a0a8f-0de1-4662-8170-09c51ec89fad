<template>
  <view class="custom-tabbar">
    {{currentPath}}
    <view v-for="(item, index) in tabList" :key="index" class="tabbar-item" :class="{ active: currentPath == item.pagePath }" @click="switchTab(index, item.pagePath)">
      <text class="tabbar-text" :class="{ active: currentPath == item.pagePath }">{{ item.text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomTabbar',
  props: {
    currentPath: { // 当前页面路径
      type: String,
      default: '/pages/chat/chat',
    },
  },
  data() {
    return {
      tabList: [
        { pagePath: '/pages/find/follow', text: '关注' },
        { pagePath: '/pages/find/find', text: '社区' },
        { pagePath: '/pages/chat/chat', text: '消息' }
      ]
    }
  },
  methods: {
    switchTab(index, path) {
      if (this.currentPath === path) return
      uni.switchTab({ url: path })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    position: relative;

    .tabbar-text {
      font-size: 24rpx;
      color: #878787;
      position: relative;
      padding-bottom: 12rpx;

      &.active {
        color: #000000;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 56rpx;
          height: 6rpx;
          background: #7414ff;
          border-radius: 3rpx;
        }
      }
    }
  }
}
</style>