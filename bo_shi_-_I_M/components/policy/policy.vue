<template>
	<view @touchmove.stop.prevent="clear" v-show="showPopup">
		<view class="popup_mask" @touchmove.stop.prevent="clear"></view>
		<view class="popup_content">
			<view class="title">服务协议和隐私政策</view>
			<view class="explain_text">
				请你务必认真阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了向你提供数据、分享等服务所要获取的权限信息。
				<view class="line">
					你可以阅读
					<text class="path" @click="onShowProtocol()">《用户协议》</text>
					和
					<text class="path" @click="onShowPrivacy()">《隐私政策》</text>
					了解详细信息。如您同意，请点击"同意"开始接受我们的服务。
				</view>
			</view>
			<view class="button">
				<view @tap="back">暂不使用</view>
				<view @tap="confirm">同意</view>
			</view>
		</view>
	</view>
</template>

<script>
import UNI_APP from '@/.env.js';

export default {
	name: "policy",
	props: {
		title: {
			type: String,
			default: "服务协议和隐私政策"
		},
		// 协议路径
		protocolPath: {
			type: String
		},
		// 政策路径
		policyPath: {
			type: String
		},
		policyStorageKey: {
			type: String,
			default: "has_read_privacy"
		}
	},
	data() {
		return {
			showPopup: false
		};
	},
	created: function () {
		// 安卓的使用uniapp自带的协议弹窗，IOS才使用自定义弹窗
		if (uni.getSystemInfoSync().platform == "ios") {
			uni.getStorage({
				key: this.policyStorageKey,
				success: (res) => {
					if (res.data) {
						this.showPopup = false;
					}
				},
				fail: () => {
					this.showPopup = true;
				}
			});
		}
	},
	methods: {
		// 禁止滚动
		clear() {
			return;
		},
		back() {
			const platform = uni.getSystemInfoSync().platform
			if (platform == "android") {
				plus.runtime.quit();
			}
			if (platform == "ios") {
				plus.ios.import("UIApplication").sharedApplication().performSelector("exit");
			}
		},
		// 关闭弹框
		confirm() {
			this.showPopup = false;
			this.$emit('popupState', true);
			uni.setStorage({
				key: this.policyStorageKey,
				data: true
			});
		},
		onShowProtocol() {
			const linkUrl = encodeURIComponent(UNI_APP.PROTOCOL_URL);
			uni.navigateTo({
				url: '/pages/common/external-link?url=' + linkUrl
			});
		},
		onShowPrivacy() {
			const linkUrl = encodeURIComponent(UNI_APP.PRIVACY_URL);
			uni.navigateTo({
				url: '/pages/common/external-link?url=' + linkUrl
			});
		},
	}
};
</script>

<style lang="scss" scoped>
.popup_mask {
	position: fixed;
	bottom: 0;
	top: 0;
	left: 0;
	right: 0;
	background-color: rgba(0, 0, 0, 0.4);
	transition-property: opacity;
	transition-duration: 0.3s;
	opacity: 0;
	z-index: 98;
}

.popup_mask {
	opacity: 1;
}

.popup_content {
	overflow: hidden;
	box-sizing: border-box;
	padding: 40upx 20upx 0 20upx;
	position: fixed;
	bottom: 30%;
	border-radius: 8px;
	left: 50%;
	margin-left: -40%;
	right: 0;
	min-height: 400upx;
	background: #ffffff;
	width: 80%;
	z-index: 99;

	.title {
		text-align: center;
		font-size: 34upx;
		padding: 10upx 0 0 0;
	}

	.explain_text {
		font-size: 30upx;
		padding: 30upx 30upx 40upx 30upx;
		line-height: 38upx;

		.line {
			display: block;

			.path {
				color: #007aff;
				display: inline-block;
				text-align: center;
			}
		}
	}

	.button {
		display: flex;
		padding: 20upx;
		align-items: center;
		font-size: 34upx;
		justify-content: space-around;
		border-top: 1upx solid #f2f2f2;

		view {
			text-align: center;
		}
	}
}
</style>