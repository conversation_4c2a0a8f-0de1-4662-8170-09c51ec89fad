<template>
	<view class="switch-bar">
		<text class="title">{{ title }}</text>
		<switch class="switch" :disabled="disabled" :checked="checked" 
		@change="onChange" style="transform:scale(0.8)"></switch>
	</view>
</template>

<script>
export default {
	name: "switch-bar",
	props: {
		title: {
			type: String,
			required: true
		},
		disabled: {
			type: Boolean,
			default: false
		},
		checked: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			value: this.checked
		}
	},
	methods: {
		onChange(e) {
			this.value = true;
			setTimeout(() => {
				this.value = false;
			}, 100)
			//this.value = false;

			this.$emit('change', e);
		}
	}

}
</script>

<style lang="scss" scoped>
.switch-bar {
	width: 100%;
	height: 100rpx;
	font-size: $im-font-size;
	color: $im-text-color;
	margin-top: 5rpx;
	background-color: white;
	line-height: 100rpx;
	display: flex;

	.title {
		flex: 1;
		margin-left: 40rpx;
	}

	.switch {
		margin-right: 40rpx;
	}
}
</style>