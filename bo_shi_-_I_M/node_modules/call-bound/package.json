{"_from": "call-bound@^1.0.2", "_id": "call-bound@1.0.4", "_inBundle": false, "_integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "_location": "/call-bound", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "call-bound@^1.0.2", "name": "call-bound", "escapedName": "call-bound", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/is-arguments", "/is-date-object", "/is-regex"], "_resolved": "https://registry.npmmirror.com/call-bound/-/call-bound-1.0.4.tgz", "_shasum": "238de935d2a2a692928c538c7ccfa91067fd062a", "_spec": "call-bound@^1.0.2", "_where": "D:\\项目\\铂时-AI商城\\box-im-main\\im-uniapp\\node_modules\\is-arguments", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/call-bound/issues"}, "bundleDependencies": false, "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "deprecated": false, "description": "Robust call-bound JavaScript intrinsics, using `call-bind` and `get-intrinsic`.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.3.0", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "gopd": "^1.2.0", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/call-bound#readme", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "license": "MIT", "main": "index.js", "name": "call-bound", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bound.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "version": "1.0.4"}