{"_from": "copy-text-to-clipboard@^3.0.1", "_id": "copy-text-to-clipboard@3.2.0", "_inBundle": false, "_integrity": "sha512-RnJFp1XR/LOBDckxTib5Qjr/PMfkatD0MUCQgdpqS8MdKiNUzBjAQBEN6oUy+jW7LI93BBG3DtMB2KOOKpGs2Q==", "_location": "/copy-text-to-clipboard", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "copy-text-to-clipboard@^3.0.1", "name": "copy-text-to-clipboard", "escapedName": "copy-text-to-clipboard", "rawSpec": "^3.0.1", "saveSpec": null, "fetchSpec": "^3.0.1"}, "_requiredBy": ["/vconsole"], "_resolved": "https://registry.npmmirror.com/copy-text-to-clipboard/-/copy-text-to-clipboard-3.2.0.tgz", "_shasum": "0202b2d9bdae30a49a53f898626dcc3b49ad960b", "_spec": "copy-text-to-clipboard@^3.0.1", "_where": "D:\\项目\\铂时-AI商城\\box-im-main\\im-uniapp\\node_modules\\vconsole", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/copy-text-to-clipboard/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Copy text to the clipboard in modern browsers (0.2 kB)", "devDependencies": {"tsd": "^0.14.0", "xo": "^0.37.1"}, "engines": {"node": ">=12"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/copy-text-to-clipboard#readme", "keywords": ["copy", "text", "clipboard", "browser", "clipboard.js", "modern"], "license": "MIT", "name": "copy-text-to-clipboard", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/copy-text-to-clipboard.git"}, "scripts": {"//test": "xo && tsd", "test": "xo"}, "type": "module", "version": "3.2.0", "xo": {"envs": ["browser"]}}