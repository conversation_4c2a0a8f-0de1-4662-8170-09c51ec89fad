{"_from": "clone@^2.1.1", "_id": "clone@2.1.2", "_inBundle": false, "_integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "_location": "/clone", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "clone@^2.1.1", "name": "clone", "escapedName": "clone", "rawSpec": "^2.1.1", "saveSpec": null, "fetchSpec": "^2.1.1"}, "_requiredBy": ["/quill"], "_resolved": "https://registry.npmmirror.com/clone/-/clone-2.1.2.tgz", "_shasum": "1b7f4b9f591f1e8f83670401600345a02887435f", "_spec": "clone@^2.1.1", "_where": "D:\\项目\\铂时-AI商城\\box-im-main\\im-uniapp\\node_modules\\quill", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}, {"name": "f<PERSON><PERSON>", "url": "https://fscherwi.github.io"}, {"name": "rictic", "url": "https://github.com/rictic"}, {"name": "<PERSON>", "url": "https://github.com/jurca"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/miserylee"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/c-w"}], "dependencies": {}, "deprecated": false, "description": "deep cloning of objects and arrays", "devDependencies": {"nodeunit": "~0.9.0"}, "engines": {"node": ">=0.8"}, "homepage": "https://github.com/pvorb/node-clone#readme", "license": "MIT", "main": "clone.js", "name": "clone", "optionalDependencies": {}, "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "scripts": {"test": "nodeunit test.js"}, "tags": ["clone", "object", "array", "function", "date"], "version": "2.1.2"}