#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/承势/铂时/bo_shi_-_I_M/node_modules/.pnpm/sass@1.90.0/node_modules/sass/node_modules:/Users/<USER>/Documents/承势/铂时/bo_shi_-_I_M/node_modules/.pnpm/sass@1.90.0/node_modules:/Users/<USER>/Documents/承势/铂时/bo_shi_-_I_M/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/承势/铂时/bo_shi_-_I_M/node_modules/.pnpm/sass@1.90.0/node_modules/sass/node_modules:/Users/<USER>/Documents/承势/铂时/bo_shi_-_I_M/node_modules/.pnpm/sass@1.90.0/node_modules:/Users/<USER>/Documents/承势/铂时/bo_shi_-_I_M/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/sass@1.90.0/node_modules/sass/sass.js" "$@"
else
  exec node  "$basedir/../.pnpm/sass@1.90.0/node_modules/sass/sass.js" "$@"
fi
