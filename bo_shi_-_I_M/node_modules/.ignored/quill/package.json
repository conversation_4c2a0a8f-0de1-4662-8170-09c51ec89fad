{"_from": "quill@^1.3.7", "_id": "quill@1.3.7", "_inBundle": false, "_integrity": "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==", "_location": "/quill", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "quill@^1.3.7", "name": "quill", "escapedName": "quill", "rawSpec": "^1.3.7", "saveSpec": null, "fetchSpec": "^1.3.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/quill/-/quill-1.3.7.tgz", "_shasum": "da5b2f3a2c470e932340cdbf3668c9f21f9286e8", "_spec": "quill@^1.3.7", "_where": "D:\\项目\\铂时-AI商城\\box-im-main\\im-uniapp", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/quilljs/quill/issues"}, "bundleDependencies": false, "config": {"ports": {"proxy": "9000", "jekyll": "4000", "karma": "9876", "webpack": "9080"}}, "dependencies": {"clone": "^2.1.1", "deep-equal": "^1.0.1", "eventemitter3": "^2.0.3", "extend": "^3.0.2", "parchment": "^1.1.4", "quill-delta": "^3.6.2"}, "deprecated": false, "description": "Your powerful, rich text editor", "devDependencies": {"babel-core": "^6.26.0", "babel-loader": "^7.1.2", "babel-plugin-istanbul": "^4.1.5", "babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "babel-preset-es2015": "^6.24.1", "css-loader": "~0.28.7", "eslint": "^4.9.0", "eslint-loader": "^1.9.0", "extract-text-webpack-plugin": "^3.0.1", "html-loader": "~0.5.1", "http-proxy": "^1.16.2", "jasmine-core": "^2.8.0", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-jasmine": "^1.1.0", "karma-sauce-launcher": "^1.2.0", "lodash": "^4.17.4", "style-loader": "~0.19.0", "stylus": "~0.54.5", "stylus-loader": "^3.0.1", "ts-loader": "^3.0.3", "typescript": "^2.5.3", "wdio-jasmine-framework": "~0.3.2", "wdio-spec-reporter": "~0.1.2", "webdriver-manager": "^12.0.6", "webdriverio": "^4.8.0", "webpack": "^3.8.1", "webpack-dev-server": "^2.9.2"}, "files": ["assets", "blots", "core", "formats", "modules", "themes", "ui", "dist/quill.bubble.css", "dist/quill.snow.css", "dist/quill.core.css", "dist/quill.js", "dist/quill.core.js", "dist/quill.min.js.map", "dist/quill.min.js", "core.js", "quill.js"], "homepage": "http://quilljs.com", "keywords": ["editor", "rich text", "wysiwyg"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/quill.js", "name": "quill", "repository": {"type": "git", "url": "git+https://github.com/quilljs/quill.git"}, "scripts": {"build": "webpack --config _develop/webpack.config.js; rm dist/quill.core dist/quill.bubble dist/quill.snow;", "build:release": "./_develop/scripts/release.sh", "start": "npm run build; bundle exec foreman start -f _develop/procfile", "test": "npm run test:unit", "test:all": "npm run test:unit; npm run test:functional", "test:coverage": "webpack --env.coverage --config _develop/webpack.config.js; karma start _develop/karma.config.js --reporters coverage", "test:functional": "./_develop/scripts/webdriver.sh", "test:unit": "npm run build; karma start _develop/karma.config.js", "travis": "karma start _develop/karma.config.js --reporters dots,saucelabs", "webdriver:start": "webdriver-manager start", "webdriver:update": "webdriver-manager update"}, "version": "1.3.7"}