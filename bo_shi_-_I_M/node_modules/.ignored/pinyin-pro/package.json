{"_from": "pinyin-pro@^3.23.1", "_id": "pinyin-pro@3.26.0", "_inBundle": false, "_integrity": "sha512-HcBZZb0pvm0/JkPhZHWA5Hqp2cWHXrrW/WrV+OtaYYM+kf35ffvZppIUuGmyuQ7gDr1JDJKMkbEE+GN0wfMoGg==", "_location": "/pinyin-pro", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pinyin-pro@^3.23.1", "name": "pinyin-pro", "escapedName": "pinyin-pro", "rawSpec": "^3.23.1", "saveSpec": null, "fetchSpec": "^3.23.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/pinyin-pro/-/pinyin-pro-3.26.0.tgz", "_shasum": "9e3b8a9f848263b81552d56e9319f520f7709ad6", "_spec": "pinyin-pro@^3.23.1", "_where": "D:\\项目\\铂时-AI商城\\box-im-main\\im-uniapp", "author": {"name": "zh-lx"}, "bugs": {"url": "https://github.com/zh-lx/pinyin-pro/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "deprecated": false, "description": "准确率和性能最优异的汉字转拼音库。获取中文拼音、韵母、声母、声调、首字母，支持拼音匹配", "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@pinyin-pro/data": "1.0.3", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "@vitest/coverage-v8": "^1.5.0", "babel-eslint": "^10.1.0", "commitizen": "^4.2.2", "eslint": "^7.22.0", "rollup": "2.60.0", "rollup-plugin-alias": "^2.2.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "typescript": "^4.2.3", "vitest": "^1.5.0"}, "exports": {".": {"import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "files": ["dist", "types"], "homepage": "https://pinyin-pro.cn", "keywords": ["拼音", "pinyin", "汉字", "中文", "首字母", "韵母", "声母", "音调"], "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.mjs", "name": "pinyin-pro", "repository": {"type": "git", "url": "git+https://github.com/zh-lx/pinyin-pro.git"}, "scripts": {"build": "rollup -c && rollup -c rollup.esm.config.js", "commit": "git-cz", "lint": "eslint .", "test": "vitest run --coverage"}, "sideEffects": false, "typings": "./types/index.d.ts", "version": "3.26.0"}