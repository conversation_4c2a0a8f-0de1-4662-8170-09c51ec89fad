export { getInitialAndFinal, getFinalParts, getNumOfTone } from './core/pinyin/handle';
export { pinyin } from './core/pinyin';
export { customPinyin, clearCustomDict } from './core/custom';
export { addDict, removeDict } from './core/dict';
export { match } from './core/match';
export { html } from './core/html';
export { polyphonic } from './core/polyphonic';
export { convert } from './core/convert';
export { segment, OutputFormat } from './core/segment';
