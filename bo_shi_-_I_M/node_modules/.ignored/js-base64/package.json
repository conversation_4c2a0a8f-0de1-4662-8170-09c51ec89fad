{"_from": "js-base64@^3.7.7", "_id": "js-base64@3.7.7", "_inBundle": false, "_integrity": "sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==", "_location": "/js-base64", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "js-base64@^3.7.7", "name": "js-base64", "escapedName": "js-base64", "rawSpec": "^3.7.7", "saveSpec": null, "fetchSpec": "^3.7.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/js-base64/-/js-base64-3.7.7.tgz", "_shasum": "e51b84bf78fbf5702b9541e2cb7bfcb893b43e79", "_spec": "js-base64@^3.7.7", "_where": "D:\\项目\\铂时-AI商城\\box-im-main\\im-uniapp", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/dankogai/js-base64/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Yet another Base64 transcoder in pure-JS", "devDependencies": {"@types/node": "^20.11.5", "mocha": "^10.2.0", "typescript": "^5.3.3"}, "exports": {".": {"import": {"types": "./base64.d.mts", "default": "./base64.mjs"}, "require": {"types": "./base64.d.ts", "default": "./base64.js"}}, "./package.json": "./package.json"}, "files": ["base64.js", "base64.mjs", "base64.d.ts", "base64.d.mts"], "homepage": "https://github.com/dankogai/js-base64#readme", "keywords": ["base64", "binary"], "license": "BSD-3-<PERSON><PERSON>", "main": "base64.js", "module": "base64.mjs", "name": "js-base64", "repository": {"type": "git", "url": "git+https://github.com/dankogai/js-base64.git"}, "scripts": {"test": "make clean && make test"}, "sideEffects": false, "types": "base64.d.ts", "version": "3.7.7"}