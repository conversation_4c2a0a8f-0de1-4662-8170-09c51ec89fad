# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.0.2](https://github.com/jfsiii/set-function-name/compare/v2.0.1...v2.0.2) - 2024-02-19

### Commits

- [meta] add types [`ae747cd`](https://github.com/jfsiii/set-function-name/commit/ae747cdcf4a986e6974a375be8ec04dd7565f0bd)
- [Dev Deps] update `aud`, `npmignore`, `object-inspect`, `tape` [`01aafcb`](https://github.com/jfsiii/set-function-name/commit/01aafcb82e4df653fcbd666ce4f256c06f70d94e)
- [Deps] update `define-data-property`, `has-property-descriptors` [`0ef6338`](https://github.com/jfsiii/set-function-name/commit/0ef6338fac84f63c2445855778bfd9271ae048cb)
- [Refactor] use `es-errors` [`0b23e87`](https://github.com/jfsiii/set-function-name/commit/0b23e878a16c959bd8b1aa7152d65818dd90578c)

## [v2.0.1](https://github.com/jfsiii/set-function-name/compare/v2.0.0...v2.0.1) - 2023-09-13

### Commits

- [Fix] move `functions-have-names` to runtime deps [`db2eda8`](https://github.com/jfsiii/set-function-name/commit/db2eda8da4c8aecfad01739000bbd63d04a8e8cf)

## [v2.0.0](https://github.com/jfsiii/set-function-name/compare/v1.0.0...v2.0.0) - 2023-09-12

### Commits

- [eslint] add `npm run lint` [`23e1fcd`](https://github.com/jfsiii/set-function-name/commit/23e1fcd85b64b864cb8f7e894da78d540e72923a)
- [actions] add reused GHA [`525127e`](https://github.com/jfsiii/set-function-name/commit/525127e08bdb35b0d44f565492456c7d1d5923fc)
- [meta] add `.gitignore` [`aa3abdf`](https://github.com/jfsiii/set-function-name/commit/aa3abdfa6af18adf70281352905dd69ef8b2c66f)
- [Tests] switch tests to use tape; add posttest [`8ad6d30`](https://github.com/jfsiii/set-function-name/commit/8ad6d30c61f78d2f9beec156ba7f1c4c3741c593)
- [readme] add readme [`732c46c`](https://github.com/jfsiii/set-function-name/commit/732c46c05e0fa6843dbc85b3af1e9c9bf22e7bf9)
- [New] add optional `loose` argument [`f5e4771`](https://github.com/jfsiii/set-function-name/commit/f5e4771266c95567d87f6dc2cff94c986bc7e074)
- [meta] relicense package to MIT; fix repo URLs [`13948f8`](https://github.com/jfsiii/set-function-name/commit/13948f8bb4ec8a25b67332d8de232ad0c0dc4e6d)
- [meta] add `auto-changelog` [`7ab201c`](https://github.com/jfsiii/set-function-name/commit/7ab201c0d83d464664cc4588acad0d0f75926679)
- [Breaking] throw if a non-function is provided [`cf6fc8f`](https://github.com/jfsiii/set-function-name/commit/cf6fc8f3396d58aa8c32a83375cbf57d933d7e79)
- [Breaking] drop UMD, just use CJS [`47abfe8`](https://github.com/jfsiii/set-function-name/commit/47abfe89abfaa72f71e0a77a52a5ff2b7377e11a)
- [Refactor] use `define-data-property` and `has-property-descriptors` [`9921c2b`](https://github.com/jfsiii/set-function-name/commit/9921c2b64c571255084aefaa02660314609e8ea0)
- [meta] use `npmignore` to autogenerate an npmignore file [`c5dbe4f`](https://github.com/jfsiii/set-function-name/commit/c5dbe4f4c44298596338432e2ac9946a10c98edd)
- Only apps should have lockfiles [`98bbfa1`](https://github.com/jfsiii/set-function-name/commit/98bbfa145387f318152a6b792bd69aca3c544813)
- [meta] add `safe-publish-latest` [`8916cd8`](https://github.com/jfsiii/set-function-name/commit/8916cd8f5549dceee31f06d35a76e2f9d4933a8b)
- [meta] add `engines` [`2427c8e`](https://github.com/jfsiii/set-function-name/commit/2427c8e2499f378c9ff3d92c7d544c771ca810df)

## v1.0.0 - 2017-09-14

### Commits

- Initial commit. Including tests. [`f26a1f2`](https://github.com/jfsiii/set-function-name/commit/f26a1f2c69a70dad5049b1daa08eae5f6acd9a1e)
- add .npmignore to ignore test directory [`9cac96f`](https://github.com/jfsiii/set-function-name/commit/9cac96fd9e98f25883be6e727ac32356522a5d69)
