(function(e){function t(t){for(var s,a,r=t[0],c=t[1],h=t[2],l=0,u=[];l<r.length;l++)a=r[l],Object.prototype.hasOwnProperty.call(o,a)&&o[a]&&u.push(o[a][0]),o[a]=0;for(s in c)Object.prototype.hasOwnProperty.call(c,s)&&(e[s]=c[s]);d&&d(t);while(u.length)u.shift()();return n.push.apply(n,h||[]),i()}function i(){for(var e,t=0;t<n.length;t++){for(var i=n[t],s=!0,r=1;r<i.length;r++){var c=i[r];0!==o[c]&&(s=!1)}s&&(n.splice(t--,1),e=a(a.s=i[0]))}return e}var s={},o={app:0},n=[];function a(t){if(s[t])return s[t].exports;var i=s[t]={i:t,l:!1,exports:{}};return e[t].call(i.exports,i,i.exports,a),i.l=!0,i.exports}a.m=e,a.c=s,a.d=function(e,t,i){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(a.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)a.d(i,s,function(t){return e[t]}.bind(null,s));return i},a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="";var r=window["webpackJsonp"]=window["webpackJsonp"]||[],c=r.push.bind(r);r.push=t,r=r.slice();for(var h=0;h<r.length;h++)t(r[h]);var d=c;n.push([0,"chunk-vendors"]),i()})({0:function(e,t,i){e.exports=i("56d7")},"143f":function(e,t,i){"use strict";i("f699")},3961:function(e,t,i){"use strict";i("4a0d")},"4a0d":function(e,t,i){},"502c":function(e,t,i){},"56d7":function(e,t,i){"use strict";i.r(t);var s={};i.r(s),i.d(s,"MESSAGE_TYPE",(function(){return ce}));var o=i("2b0e"),n=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("chat-video")],1)},a=[],r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"chat-video"},[t("audio",{ref:"callAudio",attrs:{loop:"true","x5-playsinline":"",playsinline:"","webkit-playsinline":""}},[t("source",{attrs:{src:i("cffd")}})]),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isReady&&!e.isHost,expression:"!isReady&&!isHost"}],staticClass:"call-box"},[e.inviter?t("div",{staticClass:"inv-avatar"},[t("head-image",{attrs:{url:e.inviter.headImage,name:e.inviter.nickName,size:150,isReady:e.isJSBridgeReady}},[t("div",{staticClass:"inv-name"},[e._v(e._s(e.inviter.nickName))])])],1):e._e(),t("div",{staticClass:"inv-text"},[e._v("邀请你加入多人通话")]),t("div",{staticClass:"user-list-text"},[e._v("参与通话的还有:")]),t("div",{staticClass:"user-list"},e._l(e.userInfos,(function(i){return t("div",{directives:[{name:"show",rawName:"v-show",value:i.id!=e.inviterId,expression:"user.id!=inviterId"}],key:i.id},[t("head-image",{attrs:{url:i.headImage,name:i.nickName,size:40,isReady:e.isJSBridgeReady}})],1)})),0)]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady||e.isHost,expression:"isReady||isHost"}],staticClass:"video-box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isLeaderMode,expression:"!isLeaderMode"}],staticClass:"video-list-1",attrs:{id:"videos"}},[e._l(e.userInfos,(function(i){return t("div",{key:i.id,style:e.toVideoStyle(i),attrs:{id:"video"+i.id},on:{click:function(t){return e.onClickVideo(i)}}},[i.id==e.mine.id?t("local-video",{ref:"localVideo",refInFor:!0,attrs:{userInfo:e.mine,width:e.toVw(i),height:e.toVh(i),isJSBridgeReady:e.isJSBridgeReady},on:{switchFacing:e.onSwitchFacing}}):e._e(),i.id!=e.mine.id?t("remote-video",{ref:"remoteVideo"+i.id,refInFor:!0,attrs:{userInfo:i,groupId:e.groupId,width:e.toVw(i),height:e.toVh(i),isJSBridgeReady:e.isJSBridgeReady}}):e._e()],1)})),t("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfos.length<e.config.maxChannel,expression:"userInfos.length<config.maxChannel"}],style:e.videoStyle,attrs:{id:"invite1"}},[t("invite-member",{ref:"invMember",attrs:{API:e.API,groupId:e.groupId,userInfos:e.userInfos,maxChannel:e.config.maxChannel,isJSBridgeReady:e.isJSBridgeReady},on:{ok:e.appendUser}})],1)],2),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isLeaderMode,expression:"isLeaderMode"}],staticClass:"video-list-2"},[t("div",{staticClass:"leader",attrs:{id:"leader"}}),t("div",{staticClass:"follower",attrs:{id:"follower"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfos.length<e.config.maxChannel,expression:"userInfos.length<config.maxChannel"}],style:e.videoStyle,attrs:{id:"invite2"}},[t("invite-member",{ref:"invMember",attrs:{API:e.API,groupId:e.groupId,userInfos:e.userInfos,maxChannel:e.config.maxChannel,isJSBridgeReady:e.isJSBridgeReady},on:{ok:e.appendUser}})],1)])])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady||e.isHost,expression:"isReady||isHost"}],ref:"refCtrl",staticClass:"control-bar"},[t("div",{staticClass:"chat-time"},[e._v(e._s(e.chatTimeString))]),t("div",{staticClass:"dev-bar"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&e.isMicroPhone,expression:"isReady && isMicroPhone"}],staticClass:"icon-box"},[t("div",{staticClass:"icon iconfont icon-microphone-on icon-front",on:{click:function(t){return e.onSwitchMicroPhone()}}}),t("div",{staticClass:"icon-text"},[e._v("麦克风已开")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&!e.isMicroPhone,expression:"isReady && !isMicroPhone"}],staticClass:"icon-box"},[t("div",{staticClass:"icon iconfont icon-microphone-off icon-back",on:{click:function(t){return e.onSwitchMicroPhone()}}}),t("div",{staticClass:"icon-text"},[e._v("麦克风已关")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&e.isSpeaker,expression:"isReady && isSpeaker"}],staticClass:"icon-box"},[t("div",{staticClass:"icon iconfont icon-speaker-on icon-front",on:{click:function(t){return e.onSwitchSpeaker()}}}),t("div",{staticClass:"icon-text"},[e._v("扬声器已开")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&!e.isSpeaker,expression:"isReady && !isSpeaker"}],staticClass:"icon-box"},[t("div",{staticClass:"icon iconfont icon-speaker-off icon-back",on:{click:function(t){return e.onSwitchSpeaker()}}}),t("div",{staticClass:"icon-text"},[e._v("扬声器已关")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&e.isCamera,expression:"isReady && isCamera"}],staticClass:"icon-box"},[t("div",{staticClass:"icon iconfont icon-camera-on icon-front",on:{click:function(t){return e.onSwitchCamera()}}}),t("div",{staticClass:"icon-text"},[e._v("摄像头已开")])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&!e.isCamera,expression:"isReady && !isCamera"}],staticClass:"icon-box"},[t("div",{staticClass:"icon iconfont icon-camera-off icon-back",on:{click:function(t){return e.onSwitchCamera()}}}),t("div",{staticClass:"icon-text"},[e._v("摄像头已关")])])])]),t("div",{ref:"refBot",staticClass:"bottom-bar"},[t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isHost&&!e.isChating,expression:"!isHost && !isChating"}],staticClass:"icon iconfont icon-phone-reject red",on:{click:function(t){return e.onReject()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.isHost&&!e.isChating,expression:"!isHost && !isChating"}],staticClass:"icon iconfont icon-phone-accept",on:{click:function(t){return e.onAccept()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isHost&&!e.isChating,expression:"isHost && !isChating"}],staticClass:"icon iconfont icon-phone-reject red",on:{click:function(t){return e.onCancel()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isChating,expression:"isChating"}],staticClass:"icon iconfont icon-phone-reject red",on:{click:function(t){return e.onQuit()}}})])])},c=[],h=(i("14d9"),i("0643"),i("fffc"),i("4e3e"),i("88a7"),i("271a"),i("5494"),i("cee4")),d=i("fc16");let l=function(e,t){window.plus&&(window.XMLHttpRequest=window.plus.net.XMLHttpRequest);const i=h["a"].create({baseURL:e,timeout:3e4,withCredentials:!0,headers:{"Content-Type":"application/json; charset=utf-8"}});return i.interceptors.request.use(e=>{let i=t.accessToken;return i&&(e.headers.accessToken=encodeURIComponent(i)),e},e=>Promise.reject(e)),i.interceptors.response.use(async e=>{if(200==e.data.code)return e.data.data;if(401==e.data.code){console.log("token失效，尝试重新获取");let s=t.refreshToken;return t=await i({method:"put",url:"/refreshToken",headers:{refreshToken:s}}).catch(()=>{Object(d["a"])("服务器请求异常")}),"object"!=typeof e.config.data&&(e.config.headers=void 0),i(e.config)}return Object(d["a"])(e.data.message),Promise.reject(e.data)},e=>(Object(d["a"])("服务器出了点小差，请稍后再试"),Promise.reject(e))),i};var u=l;class p{constructor(){this.configuration={},this.stream=null,this.senders=[]}}p.prototype.isEnable=function(){return window.RTCPeerConnection=window.RTCPeerConnection||window.webkitRTCPeerConnection||window.mozRTCPeerConnection,window.RTCSessionDescription=window.RTCSessionDescription||window.webkitRTCSessionDescription||window.mozRTCSessionDescription,window.RTCIceCandidate=window.RTCIceCandidate||window.webkitRTCIceCandidate||window.mozRTCIceCandidate,!!window.RTCPeerConnection},p.prototype.init=function(e){this.configuration=e,this.isAndroid11()&&this.fixAndroid()},p.prototype.setupPeerConnection=function(e){this.peerConnection=new RTCPeerConnection(this.configuration),this.peerConnection.ontrack=t=>{e(t.streams[0])}},p.prototype.setStream=function(e){this.senders.forEach(e=>{this.peerConnection.removeTrack(e)}),this.senders=[],e.getTracks().forEach(t=>{let i=this.peerConnection.addTrack(t,e);this.senders.push(i)}),this.stream=e},p.prototype.switchStream=function(e){let t=this.peerConnection.getSenders(),i=e.getVideoTracks()[0],s=e.getAudioTracks()[0];t.forEach(e=>{e.track&&"video"==e.track.kind&&e.replaceTrack(i),e.track&&"audio"==e.track.kind&&e.replaceTrack(s)})},p.prototype.onIcecandidate=function(e){this.peerConnection.onicecandidate=t=>{t.candidate&&e(t.candidate)}},p.prototype.onStateChange=function(e){this.peerConnection.oniceconnectionstatechange=t=>{let i=t.target.iceConnectionState;console.log("ICE连接状态变化: : "+i),e(i)}},p.prototype.createOffer=function(){return new Promise((e,t)=>{const i={offerToRecieveAudio:1,offerToRecieveVideo:1};this.peerConnection.createOffer(i).then(t=>{this.peerConnection.setLocalDescription(t),e(t)}).catch(e=>{t(e)})})},p.prototype.createAnswer=function(e){return new Promise((t,i)=>{this.setRemoteDescription(e);const s={offerToRecieveAudio:1,offerToRecieveVideo:1};this.peerConnection.createAnswer(s).then(e=>{this.peerConnection.setLocalDescription(e),t(e)}).catch(e=>{i(e)})})},p.prototype.setRemoteDescription=function(e){this.peerConnection.setRemoteDescription(new RTCSessionDescription(e))},p.prototype.addIceCandidate=function(e){this.peerConnection.addIceCandidate(new RTCIceCandidate(e))},p.prototype.close=function(e){this.peerConnection&&(this.peerConnection.close(),this.peerConnection.onicecandidate=null,this.peerConnection.onaddstream=null)},p.prototype.isAndroid11=function(){if(window.plus){const e=navigator.userAgent,t=e.match(/Android ([\d.]+)/);if(t&&2===t.length)return console.log("androidVersion:",t),"11"==t[1]}return!1},p.prototype.fixAndroid=function(){console.log("fixAndroid"),this.configuration.iceCandidatePoolSize=1;let e=new RTCPeerConnection(this.configuration);e.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0}).then(t=>{e.setLocalDescription(t),setTimeout(()=>{e.close(),console.log("fixAndroid close")},1e3)})};var m=p;const f=(e,t)=>new Promise((t,i)=>{window.plus.android.requestPermissions(["android.permission."+e],(function(e){console.log(e,"resultObj"),t(e)}),(function(e){i()}))});let v;const w=(e,t)=>{v=new window.plus.nativeObj.View("topMessageView",{width:"100%",height:"100%",backgroundColor:"rgba(0,0,0,0.85)"}),v.drawText(e,{top:"-100px",left:"10%",width:"80%",height:"80%"},{color:"#ffffff",size:"22px"}),v.drawText(t,{top:"-50px",left:"10%",width:"80%",height:"80%"},{color:"#ffffff",whiteSpace:"normal"}),v.show()},g=async(e,t,i,s)=>{if(window.plus&&"iOS"!==window.plus.os.name){let o=setTimeout(()=>w(t,i),300),n=await f(e,s);if(clearTimeout(o),v&&v.close(),console.log("res:",n),!n.granted[0])return!1}return!0},C=async()=>{const e="WRITE_EXTERNAL_STORAGE",t="访问媒体和文件权限说明",i="用于用户发送图片、视频、文件或上传头像等场景",s="访问媒体和文件权限未获得,此权限用于用户发送图片、视频、文件或上传头像等场景,请前往设置中打开";return g(e,t,i,s)},I=async()=>{const e="CAMERA",t="相机使用权限说明",i="用于拍照、录像、视频通话等场景",s="相机使用权限未获得,此权限用于拍照、录像、视频通话等场景,请前往设置中打开";return g(e,t,i,s)},y=async()=>{const e="RECORD_AUDIO",t="麦克风使用权限说明",i="用于拍摄、录制语音消息、视频或语音通话等场景",s="麦克风使用权限未获得,此权限用于用于拍摄、录制语音消息、视频或语音通话等场景,请前往设置中打开";return g(e,t,i,s)};var T={storage:C,camera:I,micro:y};class b{constructor(){this.stream=null}}b.prototype.isEnable=function(){return!!navigator&&!!navigator.mediaDevices&&!!navigator.mediaDevices.getUserMedia},b.prototype.openVideo=function(e){return this.stream&&this.close(),new Promise((t,i)=>{setTimeout(async()=>{if(!await T.camera())return i({message:"未能获取摄像头访问权限"});if(!await T.micro())return i({message:"未能获取麦克风权限"});let s=e?"user":"environment",o={video:{width:window.screen.width,height:window.screen.width,facingMode:s},audio:{echoCancellation:!0,noiseSuppression:!0}};navigator.mediaDevices.getUserMedia(o).then(e=>{console.log("摄像头打开"),this.stream=e,t(e)}).catch(e=>{i({message:"摄像头未能正常打开"})})})})},b.prototype.openAudio=function(){return this.stream&&this.close(),new Promise((e,t)=>{setTimeout(async()=>{if(!await T.micro())return t({message:"未能获取麦克风权限"});let i={video:!1,audio:{echoCancellation:!0,noiseSuppression:!0}};navigator.mediaDevices.getUserMedia(i).then(t=>{this.stream=t,e(t)}).catch(()=>{console.log("麦克风未能正常打开"),t({code:0,message:"麦克风未能正常打开"})})})})},b.prototype.close=function(){this.stream&&(this.stream.getTracks().forEach(e=>{e.stop()}),this.stream=null)};var R=b;class S{constructor(e,t){this.http=u(e,t)}}S.prototype.findGroupMembers=function(e){return this.http({url:"/group/members/"+e,method:"get"})},S.prototype.setup=function(e,t){let i={groupId:e,userInfos:t};return this.http({url:"/webrtc/group/setup",method:"post",data:i})},S.prototype.accept=function(e){return this.http({url:"/webrtc/group/accept?groupId="+e,method:"post"})},S.prototype.reject=function(e){return this.http({url:"/webrtc/group/reject?groupId="+e,method:"post"})},S.prototype.failed=function(e,t){let i={groupId:e,reason:t};return this.http({url:"/webrtc/group/failed",method:"post",data:i})},S.prototype.join=function(e){return this.http({url:"/webrtc/group/join?groupId="+e,method:"post"})},S.prototype.invite=function(e,t){let i={groupId:e,userInfos:t};return this.http({url:"/webrtc/group/invite",method:"post",data:i})},S.prototype.offer=function(e,t,i){let s={groupId:e,userId:t,offer:i};return this.http({url:"/webrtc/group/offer",method:"post",data:s})},S.prototype.answer=function(e,t,i){let s={groupId:e,userId:t,answer:i};return this.http({url:"/webrtc/group/answer",method:"post",data:s})},S.prototype.quit=function(e){return this.http({url:"/webrtc/group/quit?groupId="+e,method:"post"})},S.prototype.cancel=function(e){return this.http({url:"/webrtc/group/cancel?groupId="+e,method:"post"})},S.prototype.candidate=function(e,t,i){let s={groupId:e,userId:t,candidate:i};return this.http({url:"/webrtc/group/candidate",method:"post",data:s})},S.prototype.device=function(e,t,i){let s={groupId:e,isCamera:t,isMicroPhone:i};return this.http({url:"/webrtc/group/device",method:"post",data:s})},S.prototype.candidate=function(e,t,i){let s={groupId:e,userId:t,candidate:i};return this.http({url:"/webrtc/group/candidate",method:"post",data:s})},S.prototype.heartbeat=function(e){return this.http({url:"/webrtc/group/heartbeat?groupId="+e,method:"post"})};var k=S;class P{}P.prototype.listen=function(e){window.onEvent=t=>{let i=JSON.parse(decodeURIComponent(t));e(i.key,i.data)},window.addEventListener("message",(function(t){const i=t.data;e(i.key,i.data)}),!1)};var E=P,x=function(){var e=this,t=e._self._c;return t("div",{staticClass:"head-image"},[e.isReady&&e.url?t("van-image",{staticClass:"avatar-image",style:e.avatarImageStyle,attrs:{src:e.url,loading:"lazy"}}):t("div",{staticClass:"avatar-text",style:e.avatarTextStyle},[e._v(" "+e._s(e.name.substring(0,1).toUpperCase())+" ")]),e.online?t("div",{staticClass:"online",attrs:{title:"用户当前在线"}}):e._e(),e._t("default")],2)},_=[],A={name:"headImage",data(){return{colors:["#7dd24b","#c7515a","#db68ef","#15d29b","#85029b","#c9b455","#fb2609","#bda818","#af0831","#326eb6"]}},props:{size:{type:Number,default:50},width:{type:Number},radius:{type:String,default:"10%"},height:{type:Number},url:{type:String},name:{type:String,default:"?"},online:{type:Boolean,default:!1},isReady:{type:Boolean,default:!0}},methods:{},computed:{avatarImageStyle(){let e=this.width?this.width:this.size,t=this.height?this.height:this.size;return`width:${e}px; height:${t}px;\n\t\t\t\t\tborder-radius: ${this.radius};`},avatarTextStyle(){let e=this.width?this.width:this.size,t=this.height?this.height:this.size;return`width: ${e}px;height:${t}px;\n\t\t\t\t\tcolor:${this.textColor};font-size:${.6*e}px;\n\t\t\t\t\tborder-radius: ${this.radius};`},textColor(){let e=0;for(var t=0;t<this.name.length;t++)e+=this.name.charCodeAt(t);return this.colors[e%this.colors.length]}}},N=A,O=(i("143f"),i("2877")),V=Object(O["a"])(N,x,_,!1,null,"323bc5ea",null),M=V.exports,$=function(){var e=this,t=e._self._c;return t("div",{staticClass:"local-video"},[t("head-image",{directives:[{name:"show",rawName:"v-show",value:!e.isReady||!e.userInfo.isCamera,expression:"!isReady||!userInfo.isCamera"}],staticClass:"head-image",attrs:{width:e.width,height:e.height,url:e.userInfo.headImage,name:e.userInfo.nickName,radius:"0",isReady:e.isJSBridgeReady}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&e.userInfo.isCamera,expression:"isReady&&userInfo.isCamera"}],staticClass:"control-bar"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&e.isFacing,expression:"isReady && isFacing"}],staticClass:"icon iconfont icon-camera-front",on:{click:function(t){return t.stopPropagation(),e.onSwitchFacing()}}}),t("div",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&!e.isFacing,expression:"isReady && !isFacing"}],staticClass:"icon iconfont icon-camera-back",on:{click:function(t){return t.stopPropagation(),e.onSwitchFacing()}}})]),t("video",{directives:[{name:"show",rawName:"v-show",value:e.isReady&&e.userInfo.isCamera,expression:"isReady&&userInfo.isCamera"}],ref:"video",staticClass:"video",attrs:{id:"localVideo","x5-video-player-fullscreen":"true","x5-playsinline":"",playsinline:"","webkit-playsinline":"",muted:""},domProps:{muted:!0}})],1)},U=[],G={name:"localVideo",components:{HeadImage:M},data(){return{camera:new R,stream:null,isReady:!1,isFacing:!0}},props:{userInfo:{type:Object},width:{type:Number},height:{type:Number},isJSBridgeReady:{type:Boolean,default:!0}},methods:{open(e){this.stream=e,this.$refs.video.srcObject=e,document.getElementById("localVideo").muted=!0,this.$refs.video.play().catch(()=>{console.log("本地流播放异常")}),this.isReady=!!e},setMicroPhone(e){this.stream.getTracks().forEach(t=>{"audio"===t.kind&&(t.enabled=e)})},setCamera(e){this.stream.getTracks().forEach(t=>{"video"===t.kind&&(t.enabled=e)})},onSwitchFacing(){this.isFacing=!this.isFacing,this.$emit("switchFacing",this.isFacing)}}},J=G,j=(i("f7c3"),Object(O["a"])(J,$,U,!1,null,"409d3850",null)),H=j.exports,D=function(){var e=this,t=e._self._c;return t("div",{ref:"box",staticClass:"remote-video"},[t("van-loading",{directives:[{name:"show",rawName:"v-show",value:!e.isConnected,expression:"!isConnected"}],staticClass:"loading",attrs:{color:"#5870e6 ",size:"50"}}),t("head-image",{directives:[{name:"show",rawName:"v-show",value:!e.isConnected||!e.userInfo.isCamera,expression:"!isConnected||!userInfo.isCamera"}],staticClass:"head-image",attrs:{width:e.width,height:e.height,url:e.userInfo.headImage,name:e.userInfo.nickName,radius:"0",isReady:e.isJSBridgeReady}}),t("div",{staticClass:"info-bar"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfo.isMicroPhone,expression:"userInfo.isMicroPhone"}],staticClass:"icon iconfont icon-microphone-on"}),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.userInfo.isMicroPhone,expression:"!userInfo.isMicroPhone"}],staticClass:"icon iconfont icon-microphone-off"}),t("div",{staticClass:"name"},[e._v(" "+e._s(e.userInfo.nickName)+" ")])]),t("video",{directives:[{name:"show",rawName:"v-show",value:e.isConnected&&e.userInfo.isCamera,expression:"isConnected&&userInfo.isCamera"}],ref:"video",staticClass:"video",attrs:{id:"remoteVideo","x5-video-player-fullscreen":"true","x5-playsinline":"",playsinline:"","webkit-playsinline":""}})],1)},B=[],L={name:"localVideo",components:{HeadImage:M},data(){return{API:null,webrtc:new m,localStream:null,remoteStream:null,candidates:[],isInit:!1,isConnected:!1,isReady:!1}},props:{width:{type:Number},height:{type:Number},groupId:{type:String},userInfo:{type:Object},isJSBridgeReady:{type:Boolean,default:!0}},methods:{init(e,t,i){this.isInit=!0,this.API=e,this.webrtc.init(t),this.localStream=i,this.webrtc.setupPeerConnection(e=>{console.log("获取到远端流"),this.remoteStream=e,this.$refs.video.srcObject=e,this.$refs.video.play().catch(()=>{console.log("远端流播放失败")})}),this.webrtc.setStream(i),this.webrtc.onIcecandidate(e=>{this.isReady?this.API.candidate(this.groupId,this.userInfo.id,JSON.stringify(e)):this.candidates.push(e)}),this.webrtc.onStateChange(e=>{"connected"==e&&(this.isConnected=!0)})},reconnect(e){this.localStream=e,this.webrtc.setStream(e),this.connect()},switchStream(e){this.localStream=e,this.webrtc.switchStream(e)},setMute(e){const t=document.getElementById("remoteVideo");t.pause(),t.muted=e,t.play()},connect(){this.webrtc.createOffer().then(e=>{this.API.offer(this.groupId,this.userInfo.id,JSON.stringify(e))})},onOffer(e){this.webrtc.createAnswer(e).then(e=>{this.API.answer(this.groupId,this.userInfo.id,JSON.stringify(e))}),this.isReady=!0},onAnswer(e){this.webrtc.setRemoteDescription(e),this.sendCandidate(),this.isReady=!0},setCandidate(e){this.webrtc.addIceCandidate(e)},sendCandidate(){this.candidates.forEach(e=>{this.API.candidate(this.groupId,this.userInfo.id,JSON.stringify(e))}),this.candidates=[]},close(){this.webrtc.close(),this.$refs.video.srcObject=null,this.isInit=!1,this.isConnected=!1,this.isReady=!1,this.candidates=[]}}},F=L,z=(i("3961"),Object(O["a"])(F,D,B,!1,null,"483241b8",null)),Y=z.exports,W=function(){var e=this,t=e._self._c;return t("div",{staticClass:"invite-member"},[t("div",{staticClass:"invite-btn"},[t("div",{staticClass:"icon iconfont icon-invite-rtc",on:{click:function(t){return e.onPopup()}}}),t("div",{staticClass:"inv-text"},[e._v("点击邀请")])]),t("van-popup",{attrs:{position:"bottom"},model:{value:e.isPopup,callback:function(t){e.isPopup=t},expression:"isPopup"}},[t("div",{staticClass:"popup-content"},[t("div",{staticClass:"top-bar"},[t("div",{staticClass:"top-tip"},[e._v("邀请成员加入通话")]),t("van-button",{staticClass:"top-btn",attrs:{type:"danger",size:"middle"},on:{click:function(t){return e.onClean()}}},[e._v("清空 ")]),t("van-button",{staticClass:"top-btn",attrs:{type:"info",size:"middle"},on:{click:function(t){return e.onOk()}}},[e._v("确定("+e._s(e.checkedSize)+")")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.checkedSize>0,expression:"checkedSize>0"}]},[t("div",{staticClass:"checked-users"},e._l(e.members,(function(i){return t("div",{directives:[{name:"show",rawName:"v-show",value:i.checked,expression:"m.checked"}],key:i.id,staticClass:"user-item"},[t("head-image",{attrs:{name:i.showNickName,url:i.headImage,size:30,isReady:e.isJSBridgeReady}})],1)})),0)]),t("div",{staticClass:"search-bar"},[t("van-search",{attrs:{clearable:!1,"show-action":!1,placeholder:"搜索"},model:{value:e.searchText,callback:function(t){e.searchText=t},expression:"searchText"}})],1),t("div",{staticClass:"member-items"},[t("div",{staticClass:"scroll-bar",attrs:{scroll:"true"}},e._l(e.members,(function(i){return t("div",{directives:[{name:"show",rawName:"v-show",value:i.showNickName.includes(e.searchText),expression:"m.showNickName.includes(searchText)"}],key:i.userId},[t("div",{staticClass:"member-item",on:{click:function(t){return e.onClickItem(i)}}},[t("head-image",{attrs:{name:i.showNickName,online:i.online,url:i.headImage,size:50,isReady:e.isJSBridgeReady}}),t("div",{staticClass:"member-name"},[e._v(e._s(i.showNickName))]),t("div",{staticClass:"member-checked"},[t("van-checkbox",{attrs:{disabled:i.locked,"checked-color":"#0283ef"},on:{change:function(t){return e.onSwitchChecked(i)}},model:{value:i.checked,callback:function(t){e.$set(i,"checked",t)},expression:"m.checked"}})],1)],1)])})),0)])])])],1)},q=[],Q=(i("2382"),{name:"InviteMember",components:{HeadImage:M},data(){return{isPopup:!1,searchText:"",members:[]}},props:{API:{type:Object},groupId:{type:String},userInfos:{type:Array},maxChannel:{type:Number},isJSBridgeReady:{type:Boolean,default:!0}},methods:{onPopup(){this.isPopup=!0,this.API.findGroupMembers(this.groupId).then(e=>{e.forEach(e=>{e.checked=this.isExist(e.userId),e.locked=e.checked}),this.members=e.filter(e=>!e.quit)})},onSwitchChecked(e){this.checkedSize>this.maxChannel&&(e.checked=!1,this.$toast(`最多支持${this.maxChannel}人同时语音通话`))},onClickItem(e){e.locked||(e.checked=!e.checked,this.onSwitchChecked(e))},onClean(){this.members.forEach(e=>{e.locked||(e.checked=!1)})},onOk(){let e=[];this.members.forEach(t=>{t.checked&&!t.locked&&e.push({id:t.userId,nickName:t.showNickName,headImage:t.headImage,isCamera:!1,isMicroPhone:!0})}),e.length>0&&(this.API.invite(this.groupId,e),this.$emit("ok",e)),this.isPopup=!1},isExist(e){return!!this.userInfos.find(t=>t.id==e)}},computed:{checkedSize(){return this.members.filter(e=>e.checked).length}}}),X=Q,K=(i("b298"),Object(O["a"])(X,W,q,!1,null,null,null)),Z=K.exports,ee={data(){return{env:{},isJSBridgeReady:!1,camera:new R,webrtc:new m,uniEvent:new E,API:null,stream:null,isMicroPhone:!0,isFacing:!0,isSpeaker:!0,isCamera:!1,chatTime:0,chatTimer:null,heartbeatTimer:null,tipTimer:null,lastTipTime:null,state:"INIT",baseUrl:"",loginInfo:{},groupId:null,userId:null,inviterId:null,leaderId:null,isHost:!1,userInfos:[],config:{},vw:150,vh:150,leaderVw:600,leaderVh:600}},components:{HeadImage:M,LocalVideo:H,RemoteVideo:Y,InviteMember:Z},methods:{onNavBack(){this.isClose||(this.isHost&&!this.isChating?(console.log("强制终止呼叫"),this.API.cancel(this.groupId)):this.isHost||"WAITING"!=this.state?this.isChating&&(console.log("强制退出通话"),this.API.quit(this.groupId)):(console.log("强制拒绝接听"),this.API.reject(this.groupId)))},onClickVideo(e){console.log("onClickVideo"),e.id==this.leaderId?this.leaderId=null:this.leaderId=e.id,this.refreshVideoWH(),this.reLayoutVideo()},onSwitchMicroPhone(){this.isMicroPhone=!this.isMicroPhone,this.$refs.localVideo[0].setMicroPhone(this.isMicroPhone),this.API.device(this.groupId,this.isCamera,this.isMicroPhone),console.log("麦克风:"+this.isMicroPhone)},onSwitchFacing(e){this.isFacing=e,this.openStream().then(()=>{this.$refs.localVideo[0].setMicroPhone(this.isMicroPhone),this.userInfos.forEach(e=>{if(e.id!=this.mine.id){const t="remoteVideo"+e.id;this.$refs[t][0].switchStream(this.stream)}});const t=e?"前置":"后置";console.log("摄像头翻转:"+t)}).catch(e=>{this.showToast("摄像头翻转失败"),console.log("摄像头翻转失败:"+e.message)})},onSwitchCamera(){this.isCamera=!this.isCamera,this.mine.isCamera=this.isCamera,this.openStream().then(()=>{this.userInfos.forEach(e=>{if(e.id!=this.mine.id){const t="remoteVideo"+e.id;this.$refs[t][0].reconnect(this.stream)}}),this.API.device(this.groupId,this.isCamera,this.isMicroPhone),console.log("摄像头:"+this.isCamera)})},onSwitchSpeaker(){this.isSpeaker=!this.isSpeaker,this.userInfos.forEach(e=>{if(e.id!=this.mine.id){const t="remoteVideo"+e.id;this.$refs[t][0].setMute(!this.isSpeaker)}}),console.log("扬声器切换:"+this.isSpeaker)},onRTCMessage(e){if(e.type==this.$enums.MESSAGE_TYPE.RTC_GROUP_SETUP||"CLOSE"!=this.state)switch(e.type){case this.$enums.MESSAGE_TYPE.RTC_GROUP_SETUP:this.onRTCSetup(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_ACCEPT:this.onRTCAccept(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_REJECT:this.onRTCReject(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_JOIN:this.onRTCJoin(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_FAILED:this.onRTCFailed(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_CANCEL:this.onRTCCancel(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_QUIT:this.onRTCQuit(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_INVITE:this.onRTCInvite(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_OFFER:this.onRTCOffer(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_ANSWER:this.onRTCAnswer(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_CANDIDATE:this.onRTCCandidate(e);break;case this.$enums.MESSAGE_TYPE.RTC_GROUP_DEVICE:this.onRTCDevice(e);break}},onRTCSetup(e){this.$refs.callAudio.play()},onRTCAccept(e){if(e.selfSend)return void this.close("已在其他设备接听");const t=e.sendId;if(this.isChating){const e="remoteVideo"+t;this.$refs[e][0].connect()}else if(this.isHost){const e="remoteVideo"+t;this.$refs[e][0].connect(),this.state="CHATING",this.$refs.callAudio.pause(),this.startChatTime()}},onRTCReject(e){if(e.selfSend)return void this.close("已在其他设备拒绝");const t=e.sendId;this.removeUser(t,"未进入通话")},onRTCFailed(e){e.sendId;const t=JSON.parse(e.content);if(t.userIds.find(e=>e==this.mine.id))return void this.close("您未接听");let i=t.userIds[0],s=this.userInfos.find(e=>e.id==i).nickName,o=`'${s}'`;t.userIds.length>1&&(o+=`等${t.userIds.length}人`),o+="未能进入通话,原因:"+t.reason,this.showToast(o),t.userIds.forEach(e=>this.removeUser(e))},onRTCJoin(e){const t=e.sendId;let i=JSON.parse(e.content);this.isExist(t)||this.userInfos.push(i),this.$nextTick(()=>{this.initUserVideo();const e="remoteVideo"+t;this.$refs[e][0].connect()}),this.state="CHATING",this.$refs.callAudio.pause(),this.startChatTime()},onRTCOffer(e){const t=e.sendId,i=JSON.parse(e.content),s="remoteVideo"+t;this.$refs[s][0].onOffer(i)},onRTCAnswer(e){const t=e.sendId,i=JSON.parse(e.content),s="remoteVideo"+t;this.$refs[s][0].onAnswer(i)},onRTCCancel(){this.close("通话已取消")},onRTCQuit(e){const t=e.sendId;this.removeUser(t,"已退出通话")},onRTCInvite(e){let t=JSON.parse(e.content);this.appendUser(t)},onRTCCandidate(e){const t=e.sendId,i=JSON.parse(e.content),s="remoteVideo"+t;this.$refs[s][0].setCandidate(i)},onRTCDevice(e){const t=e.sendId,i=JSON.parse(e.content);let s=this.userInfos.find(e=>e.id==t);s.isCamera=i.isCamera,s.isMicroPhone=i.isMicroPhone},onAccept(){if(!this.checkDevEnable())return this.API.failed(this.groupId,"设备不支持通话"),void this.close();this.state="CHATING",this.$refs.callAudio.pause(),this.refreshVideoWH(),this.openStream().finally(()=>{this.initUserVideo(),this.API.accept(this.groupId).then(()=>{this.startChatTime()}).catch(()=>{this.close()})})},onReject(){this.API.reject(this.groupId),this.close("您拒绝了通话")},onQuit(){this.API.quit(this.groupId),this.close("退出通话")},onCancel(){this.API.cancel(this.groupId),this.close("您取消通话")},onSetup(){this.checkDevEnable()||this.close(),this.openStream().finally(()=>{this.initUserVideo(),this.API.setup(this.groupId,this.userInfos).then(()=>{this.state="READY",this.$refs.callAudio.play()}).catch(e=>{this.close()})})},onJoin(){this.checkDevEnable()||this.close(),this.state="READY",this.refreshVideoWH(),this.openStream().finally(()=>{this.initUserVideo(),this.API.join(this.groupId).then(()=>{this.state="CHATING",this.startChatTime()}).catch(e=>{this.close()})})},onInviteMember(){this.$refs.invMember.open()},appendUser(e){e.forEach(e=>{this.isExist(e.id)||(this.userInfos.push(e),console.log(`'${e.nickName}'加入通话`))}),this.$nextTick(()=>{this.initUserVideo()})},isExist(e){return!!this.userInfos.find(t=>t.id==e)},removeUser(e,t){if(!this.isExist(e))return;const i="remoteVideo"+e;this.$refs[i]&&this.$refs[i][0].close();const s=this.userInfos.findIndex(t=>t.id==e),o=this.userInfos[s];this.userInfos.splice(s,1),this.isHost&&t&&this.showToast(`'${o.nickName}'${t}`),this.userInfos.length<=1&&this.onQuit(),this.isLeader(o)&&(this.leaderId=null,this.refreshVideoWH(),this.reLayoutVideo())},openStream(){return new Promise((e,t)=>{this.isCamera?this.camera.openVideo(this.isFacing).then(t=>{console.log("摄像头打开成功"),this.stream=t,this.$refs.localVideo[0].open(t),e(t)}).catch(e=>{this.showToast(e.message),console.log("本地摄像头打开失败:"+e.message),t(e)}):this.camera.openAudio().then(t=>{console.log("麦克风打开成功"),this.stream=t,this.$refs.localVideo[0].open(t),e(t)}).catch(e=>{this.showToast(e.message),console.log("本地麦克风打开失败:"+e.message),t(e)})})},initUserVideo(){this.userInfos.forEach(e=>{if(e.id!=this.userId){const t="remoteVideo"+e.id;if(!this.$refs[t][0].isInit){const e={iceServers:this.config.iceServers};this.$refs[t][0].init(this.API,e,this.stream)}}})},startChatTime(){this.chatTimer||(this.chatTime=0,this.chatTimer=setInterval(()=>{this.chatTime++},1e3))},startHeartBeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>{this.API.heartbeat(this.groupId)},15e3)},checkDevEnable(){return this.camera.isEnable()?!!this.webrtc.isEnable()||(this.showToast("初始化RTC失败，原因可能是: 1.服务器缺少ssl证书 2.您的设备不支持WebRTC"),!1):(this.showToast("访问摄像头失败"),!1)},showToast(e){!this.lastTipTime||(new Date).getTime()-this.lastTipTime>1e3?(this.$toast(e),this.lastTipTime=(new Date).getTime(),console.log(e)):setTimeout(()=>this.showToast(e),1e3)},reLayoutVideo(){console.log("reLayoutVideo");const e=document.getElementById("leader"),t=document.getElementById("invite1"),i=document.getElementById("invite2");this.userInfos.forEach(s=>{const o="video"+s.id,n=document.getElementById(o);this.isLeaderMode?this.leaderId==s.id?e.appendChild(n):i.before(n):t.before(n)})},refreshVideoWH(){let e=window.innerWidth,t=window.innerHeight;console.log("屏幕大小",e,t);let i=this.userInfos.length;if(e&&t&&i){if(t-=520*e/750,i<this.config.maxChannel&&(i+=1),this.isLeaderMode)this.vw=e/3,this.vh=e/3,this.leaderVw=e,this.leaderVh=t-this.vh,this.leaderVh>1.2*e&&(this.leaderVh=1.2*e);else{let s=Math.ceil(Math.sqrt(i)),o=Math.ceil(i/s);this.vw=e/s,this.vh=t/o,this.vh>1.2*this.vw&&(this.vh=1.2*this.vw)}console.log("视频大小",this.vw,this.vh)}},toVw(e){return this.isLeader(e)?this.leaderVw:this.vw},toVh(e){return this.isLeader(e)?this.leaderVh:this.vh},toVideoStyle(e){return this.isLeader(e)?this.leaderStyle:this.videoStyle},isLeader(e){return this.leaderId==e.id},initEvent(){this.uniEvent.listen((e,t)=>{console.log("来自app的消息："+e+":"+JSON.stringify(t)),"RTC_MESSAGE"==e?this.onRTCMessage(t):"NAV_BACK"==e&&this.onNavBack()})},init(){this.API=new k(this.baseUrl,this.loginInfo),this.state="WAITING",this.isHost?this.onSetup():this.inviterId==this.userId&&this.onJoin(),this.startHeartBeat()},close(e){e&&this.showToast(e),this.chatTimer&&clearInterval(this.chatTimer),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),setTimeout(()=>{this.state="CLOSE",this.camera.close(),window.uni.postMessage({data:{key:"WV_CLOSE"}})},2e3),this.$refs.callAudio.pause()}},computed:{mine(){return this.userInfos.find(e=>e.id==this.userId)},inviter(){return this.userInfos.find(e=>e.id==this.inviterId)},isChating(){return"CHATING"==this.state},isReady(){return"CHATING"==this.state||"READY"==this.state},isClose(){return"CLOSE"==this.state},isWaiting(){return"WAITING"==this.state},isLeaderMode(){return!!this.leaderId},videoStyle(){return`width:${this.vw}px;height:${this.vh}px;`},leaderStyle(){return`width:${this.leaderVw}px;height:${this.leaderVh}px;`},userCount(){return this.userInfos.length},chatTimeString(){if(!this.isChating)return"";let e=Math.floor(this.chatTime/60),t=this.chatTime%60,i=e<10?"0":"";return i+=e,i+=":",i+=t<10?"0":"",i+=t,i}},watch:{userCount:{handler(e,t){this.refreshVideoWH(),this.$nextTick(()=>{this.reLayoutVideo()})}}},mounted(){const e=new URL(window.location.href);this.baseUrl=e.searchParams.get("baseUrl"),this.loginInfo=JSON.parse(e.searchParams.get("loginInfo")),this.inviterId=e.searchParams.get("inviterId"),this.userId=e.searchParams.get("userId"),this.isHost=JSON.parse(e.searchParams.get("isHost")),this.groupId=e.searchParams.get("groupId"),this.userInfos=JSON.parse(e.searchParams.get("userInfos")),this.config=JSON.parse(e.searchParams.get("config")),document.addEventListener("UniAppJSBridgeReady",()=>{this.isJSBridgeReady=!0,this.init(),this.initEvent(),window.uni.getEnv(e=>{console.log("当前环境："+JSON.stringify(e)),this.env=e}),window.uni.postMessage({data:{key:"WV_READY"}})})}},te=ee,ie=(i("d1a4"),Object(O["a"])(te,r,c,!1,null,null,null)),se=ie.exports,oe={name:"App",components:{ChatVideo:se}},ne=oe,ae=(i("8ec2"),Object(O["a"])(ne,n,a,!1,null,null,null)),re=ae.exports;const ce={RTC_GROUP_SETUP:200,RTC_GROUP_ACCEPT:201,RTC_GROUP_REJECT:202,RTC_GROUP_FAILED:203,RTC_GROUP_CANCEL:204,RTC_GROUP_QUIT:205,RTC_GROUP_INVITE:206,RTC_GROUP_JOIN:207,RTC_GROUP_OFFER:208,RTC_GROUP_ANSWER:209,RTC_GROUP_CANDIDATE:210,RTC_GROUP_DEVICE:211};i("be35");var he=i("3934");i("de63");o["a"].use(he["a"]),o["a"].prototype.$enums=s,o["a"].config.productionTip=!1,new o["a"]({render:e=>e(re)}).$mount("#app")},"8ec2":function(e,t,i){"use strict";i("c4c4")},a553:function(e,t,i){},b298:function(e,t,i){"use strict";i("a553")},be35:function(e,t,i){},c4c4:function(e,t,i){},cffd:function(e,t,i){e.exports=i.p+"media/call.038ab63f.wav"},d1a4:function(e,t,i){"use strict";i("502c")},d8b3:function(e,t,i){},f699:function(e,t,i){},f7c3:function(e,t,i){"use strict";i("d8b3")}});
//# sourceMappingURL=app.42a3c2a5.js.map