# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Package manager lock files (keep pnpm-lock.yaml but ignore others)
package-lock.json
yarn.lock

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Uniapp specific
unpackage/
.hbuilderx/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage
.grunt

# Compiled binary addons
build/Release

# Users Environment Variables
.lock-wscript

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# 微信小程序
project.config.json

# 支付宝小程序
mini.project.json

# 百度小程序
project.swan.json

# 字节跳动小程序
project.config.json

# QQ小程序
project.config.json

# 快手小程序
project.config.json

# 华为快应用
manifest.json

# 360小程序
project.config.json

# 开发工具生成的文件
.hbuilderx
.vscode
.idea

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 证书文件
*.p12
*.mobileprovision
*.keystore

# 构建输出目录
/dist/
/build/

# 本地配置文件
config.local.js
!/.trae/
