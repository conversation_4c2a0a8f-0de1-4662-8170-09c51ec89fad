<template>
	<view class="page">
		<!-- 搜索板块 -->
		<view class="page-title">
			<image src="https://boshi.channce.com/imgs/yxstatic/logo.png" mode="widthFix"></image>
			<!-- <uni-search-bar @confirm="search" :focus="false" v-model="searchValue" cancelButton="none" placeholder="输入关键词搜索"></uni-search-bar> -->
		</view>
		<!-- 轮播图板块 -->
		<swiper class="page-bannar" circular indicator-dots="true" style="" autoplay="2000">
			<swiper-item class="pageBannar-item" v-for="(item, index) in bannarList" :key="index">
				<image :src="item.pictureUrl | jointPic" mode="widthFix"></image>
				<!-- <image src="https://boshi.channce.com/imgs/yxstatic/bannar/<EMAIL>" mode="widthFix"></image> -->
			</swiper-item>
		</swiper>
		<!-- 分类轮播 -->
		<view class="category" v-if="navlist.length > 0">
			<view class="category-box">
				<view class="category-list">
					<view class="icon" v-for="(item, ind) in navlist" :key="ind" :class="classifyId == item.id ? 'is_li' : ''" @tap="changeCate(item)">
						<image mode="aspectFill" :src="item.classifyLogo | jointPic"></image>
						<view>{{ item.classifyName }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 热销榜单 -->
		<view class="stroe-list">
			<view class="selectTop">
				<view class="selectTop-add">
					<view v-if="search.area" @click="addressShow = true">{{search.area}}</view>
					<view v-else @click="addressShow = true">地区选择</view>
					<pickerAddress v-model="addressShow" @confirm="addresspick" />
				</view>
				<view class="selectTop-box">
					<view class="selectTop-select">
						<view v-for="item in typelist" :key="item.index" :class="value === item.id ? 'is_li' : ''" @click="changeType(item)"> {{ item.name }} </view>
					</view>
				</view>
			</view>
			<view class="stroeList-item" v-for="(item, index) in goodsList" :key="index" @click="goPage('/pages/userMall/goodsList', `userId=${item.userId}&id=${item.id}`)">
				<image class="stroeListItem-pic" :src="item.storeLarge | jointPic" mode="aspectFill"></image>
				<view class="stroeListItem-right">
					<view class="top">
						<view class="">{{ item.merchantName }}</view>
						<view class=""> >20km </view>
					</view>
					<view class="bottom">
						<view class="money">{{ item.classifyName }}</view>
					</view>
					<view class="address">
						<uni-icons type="location-filled" size="15"></uni-icons>
						<view class="">{{ item.storeAddress }}</view>
					</view>
				</view>
			</view>
			<luanqing-empty :show="goodsList.length == 0" text="没有更多数据啦" textColor="#000"></luanqing-empty>
		</view>
	</view>
</template>

<script>
	import pickerAddress from '@/components/liudx-pickerAddress/index.vue'
	export default {
		components: {
			pickerAddress
		},
		data() {
			return {
				bannarList: [],
				searchValue: "",
				search: { area: '' },
				typelist: [
					{ name: '最热商家', id: 3 },
					{ name: '最近入住', id: 1 }
				],
				value: 3,
				current: 0,
				goodsList: [],
				navlist: [],
				index: 0,
				currentId: 1,
				page: 1,
				classifyId: 0,
				addressShow: false,
				areaType: 0,
				addressObj: {
					province: {
						AreaId: ""
					},
					city: {
						AreaId: ""
					},
					area: {
						AreaId: ""
					},
				}
			};
		},
		onLoad() {
			this.getData();
			this.loadData();
		},
		methods: {
			changeType(item) {
				this.value = item.id;
				this.goodsList = []
				this.loadData();
			},
			getData() {
				// 获取bannar图
				this.http({
					url: '/api/advert/carouselPictures',
					success: res => { this.bannarList = res.data; }
				});
				this.http({
					url: '/api/bdc/cL',
					method: 'post',
					data: {
						limit: 10,
						offset: this.page
					},
					success: res => {
						this.navlist = res.data.rows;
					}
				});
			},
			addresspick(obj) {
				let arr = ['province', 'city', 'area'];
				let place = '';
				arr.map(key => {
					this.search[key] = obj[key].AreaId
					place += obj[key].AreaName
				})
				this.areaType = 3
				this.search.area = obj.area.AreaName
				this.addressObj = obj
				this.goodsList = []
				this.loadData()
			},

			loadData() {
				this.http({
					url: '/api/bdm/ml',
					method: 'post',
					data: {
						limit: 10,
						offset: 1,
						merchantName: '',
						sortType: this.value,
						areaType: this.areaType,
						classifyId: this.classifyId,
						provincialCode: this.addressObj.province.AreaId,
						cityCode: this.addressObj.city.AreaId,
						districtCode: this.addressObj.area.AreaId,
					},
					success: res => {
						res.data.rows.forEach(item => {
							this.goodsList.push(item);
						});
					}
				});
			},
			changeCate(item) {
				this.classifyId = item.id
				this.goodsList = [];
				this.loadData()
			},
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex;
					this.currentId = this.classify[e.currentIndex].id;
				}
				this.goodsList = [];
				this.loadData();
			},
			navToList(goodsId) {
				uni.navigateTo({
					url: `/pages/product/product?goodsId=${goodsId}&type=1&stutas=1`
				});
			},
			goPage(route, data) {
				// this.$api.msg('暂未开放');
				uni.navigateTo({
					url: `${route}?${data}`
				});
			}
		}
	};
</script>

<style lang="scss">
	.page {
		background-image: url('https://boshi.channce.com/imgs/yxstatic/bg/background.png');
		background-size: 100%;
		background-repeat: no-repeat;
		padding-top: 60rpx;
		background-color: #ffffff;
	}

	.page-title {
		height: 140rpx;
		width: 95vw;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: space-between;

		image {
			width: 35%;
		}

		::v-deep .uni-searchbar {
			width: 60%;

			.uni-searchbar__box {
				border-radius: 100rpx !important;
			}
		}
	}

	// 轮播图板块
	.page-bannar {
		height: 340rpx;

		.pageBannar-item {
			text-align: center;

			image {
				width: 95%;
				height: 100%;
			}
		}
	}


	.selectTop {
		margin-bottom: 40rpx;
		display: flex;
		justify-content: space-between;
		width: 95vw;
		margin: 0 auto;

		.selectTop-add {
			font-size: 24rpx;
			color: #9F9F9F;
			text-align: end;
		}

		.selectTop-box {
			overflow: auto;

			.selectTop-select {
				display: flex;
				align-items: center;
				position: relative;

				view {
					font-size: 14px;
					color: #000;
					text-decoration: none;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-left: 20px;
					height: 20px;
					position: relative;
					z-index: 999;
				}

				.is_li {
					font-weight: 600;
				}

				.is_li::after {
					content: "";
					background-color: rgba(25, 101, 194, .5);
					height: 5px;
					width: 100%;
					position: absolute;
					bottom: 0;
				}

			}
		}


	}

	.category {
		// background-color: #ffffff;
		width: 95vw;
		margin: 20rpx auto;
		// padding: 20rpx;
		border-radius: 10rpx;
		// background-image: linear-gradient(to bottom, #fef7e0, #ffffff);

		.category-box {
			overflow: auto;

			.category-list {
				display: flex;
				justify-content: flex-start;
				flex-flow: wrap;

				.icon {
					height: 45%;
					width: 20%;
					text-align: center;

					image {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
					}

					view {
						font-size: 22upx;
						color: #666;
						display: flex;
						justify-content: center;
					}
				}

				.is_li {
					image {
						border: 1px solid #C80348;
					}

					view {
						color: #C80348;
						font-weight: 600;
					}
				}
			}
		}

		.category-box::-webkit-scrollbar {
			display: none;
		}

		.dots {
			display: flex;
			justify-content: center;
			height: 15upx;
			width: 100%;

			view {
				width: 30upx;
				height: 5upx;
				background-color: rgba(0, 0, 0, 0.2);

				&.active {
					background-color: #ff570a;
				}
			}
		}
	}

	.stroe-list {
		margin-top: 20upx;
		padding-top: 10px;
		height: 47vh;

		.stroeList-item {
			width: 95vw;
			margin: 10px auto 20upx;
			background-color: #ffffff;
			box-shadow: 0px 0px 6px 2px #f1f1f1;
			color: #000000;
			font-size: 14px;
			margin-bottom: 40upx;
			display: flex;
			padding: 30rpx;
			border-radius: 10rpx;
			justify-content: space-between;

			.stroeListItem-pic {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
			}

			.stroeListItem-right {
				width: calc(100% - 190rpx);

				.top {
					font-size: 30rpx;
					font-weight: 600;
					display: -webkit-box;
					overflow: hidden;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					display: flex;
					justify-content: space-between;

					:nth-child(2) {
						font-size: 12px;
						color: #9F9F9F;
						font-weight: 400;
					}
				}

				.bottom {
					display: flex;
					width: 100%;
					align-items: center;
					padding: 20rpx 0;

					.money {
						color: rgba(236, 65, 19, 1);
						font-size: 28rpx;
					}

					.btn {
						color: rgba(171, 168, 168, 0.88);
						font-size: 24rpx;
						text-decoration: line-through;
						margin-left: 2px;
					}
				}

				.address {
					color: #ababab;
					display: flex;
					align-items: center;
					font-size: 12px;
				}

				.look {
					background-color: #ffebd8;
					color: #e08e40;
					display: inline-block;
					margin-top: 40rpx;
					padding: 4rpx 10rpx;
				}
			}
		}
	}
</style>