<template>
	<view class="page">
		<!-- 头部轮播 -->
		<view class="page-bannar">
			<swiper class="pageBannarSwiper" circular indicator-dots="true" style="" autoplay="2000">
				<swiper-item class="pageBannarSwiper-item" v-for="(item, index) in 3" :key="index">
					<image :src="merchant.storeLarge | jointPic" mode="widthFix"></image>
				</swiper-item>
			</swiper>
		</view>
		<view class="store">
			<view class="store-tit">
				<view class="store-tit-left">
					<view class="store-tit-left-tit">{{merchant.merchantName}}</view>
					<view class="store-tit-left-msg">
						<text class="b-r">分享 0</text>
						<text class="b-r">浏览 0</text>
						<text>关注 0</text>
					</view>
				</view>
				<!-- <view class="">
					关注
				</view> -->
			</view>
			<!-- <view class="store-msg">
				<uni-icons type="map" size="20"></uni-icons>
				<view class="marignL">
					{{merchant.storeIntro}}
				</view>
			</view> -->
			<view class="store-msg hCenter">
				<uni-icons type="image" size="20"></uni-icons>
				<view class="msg-pic marignL">
					<image :src="merchant.storePicture1 | jointPic" @click="showPic(merchant.storePicture1)" mode="scaleToFill"></image>
					<image :src="merchant.storePicture2 | jointPic" @click="showPic(merchant.storePicture2)" mode="scaleToFill"></image>
					<image :src="merchant.storePicture3 | jointPic" @click="showPic(merchant.storePicture3)" mode="scaleToFill"></image>
				</view>
			</view>
			<!-- <view class="store-msg">
				<uni-icons type="location-filled" size="20"></uni-icons>
				<view class="msg-mobile marignL">
					<view class="">{{merchant.storeAddress}}</view>
					<image src="../../static/icon/dsh.png" mode="widthFix"></image>
				</view>
				<view class="msg-btn" @click="navigation()">导航</view>
			</view> -->
		</view>
		<view class="stroeList-item" v-for="(item, index) in goodsList" :key="index" @click="goPage('/pages/userMall/detail', `userId=${options.userId}&id=${item.id}`)">
			<image class="stroeListItem-pic" :src="item.pic | jointPic" mode="aspectFill"></image>
			<view class="stroeListItem-right">
				<view class="top">
					<view class="">{{ item.name }}</view>
					<!-- <view class="" v-if="item.jl"> {{item.jl}}km </view> -->
				</view>
				<view class="bottom">
					<view class="money">￥{{ item.price }}</view>
				</view>
			</view>
		</view>

		<uni-popup ref="popup" type="center">
			<image :src="bigPic | jointPic" mode="widthFix"></image>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				items: ['精选好物', '男装', '女装', '酒水', '饮料'],
				current: 0,
				carouselList: [],
				searchValue: '',
				goodsList: [],
				merchantId: null,
				merchant: {},
				bigPic: ""
			};
		},
		onShow() {},
		onLoad(options) {
			this.options = options;
			this.getData();
			this.loadData();
		},
		methods: {
			getData() {
				this.http({
					url: '/api/bdm/md',
					method: 'post',
					data: { merchantId: this.options.id },
					success: res => {
						this.merchant = res.data;
					},
					fail: err => {
						console.log(res.message);
					}
				});
			},
			loadData() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							issueUserId: this.options.userId,
							limit: 10,
							offset: 1
						}, true),
						a_m: 'fitment_fitmentCl'
					},
					success: res => {
						res.data.rows.forEach(item => {
							this.goodsList.push(item);
						});
					}
				});
			},
			goPage(route, data) {
				uni.navigateTo({
					url: `${route}?${data}`
				});
			},
			navToList(goodsId) {
				uni.navigateTo({
					url: `/pages/product/product?goodsId=${goodsId}`
				});
			},
			onClickItem(e) {
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
			},
			search() {},
			clear() {},
			navigation() {
				/*#ifdef H5*/
				console.log('H5端')
				window.location.href = `https://uri.amap.com/marker?position=${this.merchant.storeLocal}`
				/*#endif*/

				/*#ifdef APP-PLUS*/
				console.log('APP端')
				plus.runtime.openURL(`https://uri.amap.com/marker?position=${this.merchant.storeLocal}`);
				/*#endif*/



			},
			showPic(item) {
				this.bigPic = item
				this.$refs.popup.open()
			}
		}
	};
</script>

<style lang="scss">
	.page {
		padding-top: 30rpx;

		// 轮播图板块
		.page-bannar {
			.pageBannarSwiper {
				height: 340rpx;

				.pageBannarSwiper-item {
					text-align: center;

					image {
						width: 95%;
						height: 100%;
						border-radius: 10rpx;

					}

					.fullscreen-video {
						width: 95%;
						height: 100%;
						border-radius: 10rpx;
					}
				}
			}
		}

		.store {
			width: 95vw;
			margin: 20rpx auto;
			padding: 20rpx;
			border-radius: 10rpx;
			background: #ffffff;

			.store-tit {
				display: flex;
				justify-content: space-between;
				padding-bottom: 10px;

				.store-tit-left {
					width: 70%;

					.store-tit-left-tit {
						font-weight: bold;
						font-size: 36rpx;
						padding-bottom: 5px;
					}

					.store-tit-left-msg {
						font-size: 24rpx;
						color: #a7a7a7;

						text {
							padding-right: 5px;
							padding-left: 5px;
						}

						.b-r {
							border-right: 1px solid #a7a7a7;
						}
					}
				}
			}

			.store-msg {
				display: flex;
				padding-top: 40rpx;
				padding-bottom: 40rpx;
				border-top: 1px solid #ececec;
				align-items: center;


				.marignL {
					margin-left: 10px;
					font-size: 14px;
					line-height: 18px;
				}

				.msg-pic {
					image {
						width: 50px;
						height: 50px;
						margin-right: 10px;
					}
				}

				.msg-mobile {
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex: 1;

					view {}

					image {
						width: 20px;
					}
				}

				.msg-btn {
					border-left: 1px solid #ececec;
					padding-left: 20rpx;
					font-size: 13px;
				}
			}

			.hCenter {
				align-items: center;
			}

			.store-pic {
				padding-top: 40rpx;
				padding-bottom: 30rpx;

				image {
					width: 300rpx;
				}
			}

			.store-time {
				padding: 5px 0;
			}

			.store-mobile {
				padding: 5px 0;
			}

			.order-content {
				padding: 30upx 0;
				display: flex;
				align-items: center;
				border-top: 1px solid #e7e7e7;

				.icon-shouhuodizhi {
					flex-shrink: 0;
					display: flex;
					align-items: center;
					justify-content: center;
					width: 90upx;
					color: #888;
					font-size: 44upx;
				}

				.cen {
					display: flex;
					flex-direction: column;
					flex: 1;
					font-size: 28upx;
					color: $font-color-dark;
				}

				.name {
					font-size: 34upx;
					margin-right: 24upx;
				}

				.address {
					margin-top: 16upx;
					margin-right: 20upx;
					color: $font-color-light;
				}

				.icon-you {
					font-size: 32upx;
					color: $font-color-light;
					margin-right: 30upx;
				}
			}
		}

		.commodity-module {
			width: 95vw;
			margin: 0 auto 0;

			.goodList {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.goodList-item {
					width: 48%;
					border-radius: 10px 10px 10px 10px;
					background-color: #ffffff;
					color: #000000;
					font-size: 14px;
					margin-bottom: 40upx;
					box-shadow: 0px 0px 5px -1px #a7a7a7;

					image {
						width: 100%;
						height: 350rpx;
						border-radius: 10px 10px 0px 0px;
					}

					.right {
						width: 100%;
						padding: 10rpx 20rpx;

						.top {
							font-size: 30rpx;
							font-weight: 600;
							display: -webkit-box;
							overflow: hidden;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}

						.bottom {
							display: flex;
							width: 100%;
							align-items: center;
							padding-top: 5rpx;

							.money {
								color: rgba(236, 65, 19, 1);
								font-size: 28rpx;
							}

							.btn {
								color: rgba(171, 168, 168, 0.88);
								font-size: 24rpx;
								text-decoration: line-through;
								margin-left: 2px;
							}
						}
					}
				}
			}
		}

		.stroeList-item {
			width: 95vw;
			margin: 10px auto 20upx;
			background-color: #ffffff;
			box-shadow: 0px 0px 6px 2px #f1f1f1;
			color: #000000;
			font-size: 14px;
			margin-bottom: 40upx;
			display: flex;
			padding: 30rpx;
			border-radius: 10rpx;
			justify-content: space-between;

			.stroeListItem-pic {
				width: 160rpx;
				height: 160rpx;
				border-radius: 10rpx;
			}

			.stroeListItem-right {
				width: calc(100% - 190rpx);

				.top {
					font-size: 30rpx;
					font-weight: 600;
					display: -webkit-box;
					overflow: hidden;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					display: flex;
					justify-content: space-between;

					:nth-child(2) {
						font-size: 12px;
						color: #9F9F9F;
						font-weight: 400;
					}
				}

				.bottom {
					display: flex;
					width: 100%;
					align-items: center;
					padding: 20rpx 0;

					.money {
						color: rgba(236, 65, 19, 1);
						font-size: 28rpx;
					}

					.btn {
						color: rgba(171, 168, 168, 0.88);
						font-size: 24rpx;
						text-decoration: line-through;
						margin-left: 2px;
					}
				}

				.address {
					color: #ababab;
					display: flex;
					align-items: center;
					font-size: 12px;
				}

				.look {
					background-color: #ffebd8;
					color: #e08e40;
					display: inline-block;
					margin-top: 40rpx;
					padding: 4rpx 10rpx;
				}
			}
		}
	}
</style>