<template>
	<view class="content">
		<view class="navbar">
			<view v-for="(item, index) in navList" :key="index" class="nav-item" :class="{ current: tabCurrentIndex === index }" @click="tabClick(index)">{{ item.text }}</view>
		</view>

		<swiper :current="tabCurrentIndex" class="swiper-box" duration="300" @change="changeTab">
			<swiper-item class="tab-content" v-for="(tabItem, tabIndex) in navList" :key="tabIndex">
				<scroll-view class="list-scroll-content" scroll-y @scrolltolower="loadData">
					<!-- 空白页 -->
					<empty v-if="tabItem.loaded === true && orderList.length === 0"></empty>
					<!-- 订单列表 -->
					<view v-for="(item, index) in orderList" :key="index" class="order-item">
						<view class="i-top b-b">
							<text class="time" v-if="tabItem.state == 3">{{ item.selfOrder.tradeTime | formatDate }}</text>
							<text class="time" v-else>{{ item.time | formatDate }}</text>
							<text class="state" :style="{ color: item.stateTipColor }">{{ item.stateTip }}</text>
						</view>
						<view class="goods-box-single">
							<image class="goods-img" :src="item.mcPic | jointPic" mode="widthFix"></image>
							<view class="right">
								<view class="right-top">
									<text class="title clamp">{{ item.mcName }}</text>
									<text class="attr-box">{{ item.standard }} x {{ item.numbers }}</text>
								</view>
								<text class="price">{{ item.money }}</text>
								<view class="orderId">订单号：{{ item.orderNo }}</view>
							</view>
						</view>

						<view class="price-box">
							共
							<text class="num">{{ item.numbers }}</text>
							件商品 实付款
							<text class="price">{{ item.money }}</text>
						</view>
					</view>

					<uni-load-more :status="tabItem.loadingType"></uni-load-more>
				</scroll-view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue';
import empty from '@/components/empty';
export default {
	components: {
		uniLoadMore,
		empty
	},
	data() {
		return {
			tabCurrentIndex: 0,
			navList: [
				{
					state: 0,
					text: '已提交凭证',
					loadingType: 'more',
					orderList: []
				},
				{
					state: 1,
					text: '确认打款',
					loadingType: 'more',
					orderList: []
				},
				{
					state: 2,
					text: '已取消',
					loadingType: 'more',
					orderList: []
				}
			],
			orderList: [],
			page: 1
		};
	},

	onLoad(options) {
		this.tabCurrentIndex = +options.state;
	},
	onShow() {
		// #ifndef MP
		this.loadData();
		// #endif
		// #ifdef MP
		if (options.state == 0) {
			this.loadData();
		}
		// #endif
	},
	methods: {
		//获取订单列表
		loadData(source) {
			//这里是将订单挂载到tab列表下
			let index = this.tabCurrentIndex;
			let navItem = this.navList[index];
			let state = navItem.state;

			// if (source === 'tabChange' && navItem.loaded === true) {
			// 	//tab切换只有第一次需要加载数据
			// 	return;
			// }
			if (navItem.loadingType === 'loading') {
				//防止重复加载
				return;
			}
			navItem.loadingType = 'loading';
			setTimeout(() => {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({ limit: 10, offset: this.page, status: state, userId: uni.getStorageSync('userInfo').userId }, true),
						a_m: 'merchant_userOrderList'
					},
					success: res => {
						for (let i = 0; i < res.data.rows.length; i++) {
							this.orderList.push(res.data.rows[i]);
						}
					}
				});
				//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页
				this.$set(navItem, 'loaded', true);
				this.page++;
				//判断是否还有数据， 有改为 more， 没有改为noMore
				navItem.loadingType = 'more';
			}, 600);
		},
		//swiper 切换
		changeTab(e) {
			this.orderList = [];
			this.page = 1;
			this.tabCurrentIndex = e.target.current;
			this.loadData('tabChange');
		},
		//顶部tab点击
		tabClick(index) {
			this.orderList = [];
			this.page = 1;
			this.tabCurrentIndex = index;
		},
		confirmOrder(orderTradeId) {
			this.http({
				url: '/api/so/confirmReceipt',
				method: 'post',
				data: {
					orderNo: orderTradeId,
					userId: uni.getStorageSync('userInfo').userId
				},
				success: res => {
					this.$api.msg(res.message);
				},
				fail: err => {
					console.log(res.message);
				}
			});
		},
		confirmPay(orderTradeId) {
			this.http({
				data: {
					a_d: this.$rsa.encrypt(
						{
							orderNo: orderTradeId,
							payWay: 10,
							userId: uni.getStorageSync('userInfo').userId
						},
						true
					),
					a_m: 'so_affirmPay'
				},
				success: res => {
					this.$api.msg(res.message);
					if (res.code == 2000) {
						if (res.data.ra_Code == 100) {
							var reg = /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
							var urlStr = encodeURI(res.data.rc_Result.match(reg));
							plus.runtime.openURL(urlStr);
							setTimeout(() => {
								uni.redirectTo({
									url: `/pages/money/paySuccess`
								});
							}, 2000);
						} else {
							this.$api.msg(res.data.rb_CodeMsg);
						}
					}
				},
				fail: err => {
					this.$api.msg(res.message);
				}
			});
		},
		//订单状态文字和颜色
		orderStateExp(state) {
			let stateTip = '',
				stateTipColor = '#fa436a';
			switch (+state) {
				case 1:
					stateTip = '待发货';
					break;
				case 2:
					stateTip = '待收获';
					break;
				case 9:
					stateTip = '已取消';
					stateTipColor = '#909399';
					break;

				//更多自定义
			}
			return {
				stateTip,
				stateTipColor
			};
		}
	}
};
</script>

<style lang="scss">
page,
.content {
	background: $page-color-base;
	height: 100%;
}

.swiper-box {
	height: calc(100% - 40px);
}

.list-scroll-content {
	height: 100%;
}

.navbar {
	display: flex;
	height: 40px;
	padding: 0 5px;
	background: #fff;
	box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);
	position: relative;
	z-index: 10;

	.nav-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		font-size: 15px;
		color: $font-color-dark;
		position: relative;

		&.current {
			color: $base-color;

			&:after {
				content: '';
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				width: 44px;
				height: 0;
				border-bottom: 2px solid $base-color;
			}
		}
	}
}

.uni-swiper-item {
	height: auto;
}

.order-item {
	display: flex;
	flex-direction: column;
	padding-left: 30upx;
	background: #fff;
	margin-top: 16upx;

	.i-top {
		display: flex;
		align-items: center;
		height: 80upx;
		padding-right: 30upx;
		font-size: $font-base;
		color: $font-color-dark;
		position: relative;

		.time {
			flex: 1;
		}

		.state {
			color: $base-color;
		}

		.del-btn {
			padding: 10upx 0 10upx 36upx;
			font-size: $font-lg;
			color: $font-color-light;
			position: relative;

			&:after {
				content: '';
				width: 0;
				height: 30upx;
				border-left: 1px solid $border-color-dark;
				position: absolute;
				left: 20upx;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}

	/* 多条商品 */
	.goods-box {
		height: 160upx;
		padding: 20upx 0;
		white-space: nowrap;

		.goods-item {
			width: 120upx;
			height: 120upx;
			display: inline-block;
			margin-right: 24upx;
		}

		.goods-img {
			display: block;
			width: 100%;
			height: 100%;
		}
	}

	/* 单条商品 */
	.goods-box-single {
		display: flex;
		padding: 20upx 0;

		.goods-img {
			display: block;
			width: 170upx;
			height: 170upx;
		}

		.right {
			flex: 1;
			display: flex;
			flex-direction: column;
			padding: 0 30upx 0 24upx;
			overflow: hidden;
			.right-top {
				display: flex;
				padding-bottom: 10rpx;
				.title {
					font-size: $font-base + 2upx;
					color: $font-color-dark;
					line-height: 1;
					max-width: 50%;
					// overflow: hidden;
				}

				.attr-box {
					font-size: $font-sm + 2upx;
					color: $font-color-light;
					padding: 0 12upx;
				}
			}
			.orderId {
				font-size: $font-sm + 2upx;
				color: $font-color-light;
				padding: 40rpx 0 12upx;
			}

			.price {
				font-size: $font-base + 2upx;
				color: $font-color-dark;

				&:before {
					content: '￥';
					font-size: $font-sm;
					margin: 0 2upx 0 8upx;
				}
			}
		}
	}

	.price-box {
		display: flex;
		justify-content: flex-end;
		align-items: baseline;
		padding: 20upx 30upx;
		font-size: $font-sm + 2upx;
		color: $font-color-light;

		.num {
			margin: 0 8upx;
			color: $font-color-dark;
		}

		.price {
			font-size: $font-lg;
			color: $font-color-dark;

			&:before {
				content: '￥';
				font-size: $font-sm;
				margin: 0 2upx 0 8upx;
			}
		}
	}

	.action-box {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		height: 100upx;
		position: relative;
		padding-right: 30upx;
	}

	.action-btn {
		width: 160upx;
		height: 60upx;
		margin: 0;
		margin-left: 24upx;
		padding: 0;
		text-align: center;
		line-height: 60upx;
		font-size: $font-sm + 2upx;
		color: $font-color-dark;
		background: #fff;
		border-radius: 100px;

		&:after {
			border-radius: 100px;
		}

		&.recom {
			background: #fff9f9;
			color: $base-color;

			&:after {
				border-color: #f7bcc8;
			}
		}
	}
}

/* load-more */
.uni-load-more {
	display: flex;
	flex-direction: row;
	height: 80upx;
	align-items: center;
	justify-content: center;
}

.uni-load-more__text {
	font-size: 28upx;
	color: #999;
}

.uni-load-more__img {
	height: 24px;
	width: 24px;
	margin-right: 10px;
}

.uni-load-more__img > view {
	position: absolute;
}

.uni-load-more__img > view view {
	width: 6px;
	height: 2px;
	border-top-left-radius: 1px;
	border-bottom-left-radius: 1px;
	background: #999;
	position: absolute;
	opacity: 0.2;
	transform-origin: 50%;
	animation: load 1.56s ease infinite;
}

.uni-load-more__img > view view:nth-child(1) {
	transform: rotate(90deg);
	top: 2px;
	left: 9px;
}

.uni-load-more__img > view view:nth-child(2) {
	transform: rotate(180deg);
	top: 11px;
	right: 0;
}

.uni-load-more__img > view view:nth-child(3) {
	transform: rotate(270deg);
	bottom: 2px;
	left: 9px;
}

.uni-load-more__img > view view:nth-child(4) {
	top: 11px;
	left: 0;
}

.load1,
.load2,
.load3 {
	height: 24px;
	width: 24px;
}

.load2 {
	transform: rotate(30deg);
}

.load3 {
	transform: rotate(60deg);
}

.load1 view:nth-child(1) {
	animation-delay: 0s;
}

.load2 view:nth-child(1) {
	animation-delay: 0.13s;
}

.load3 view:nth-child(1) {
	animation-delay: 0.26s;
}

.load1 view:nth-child(2) {
	animation-delay: 0.39s;
}

.load2 view:nth-child(2) {
	animation-delay: 0.52s;
}

.load3 view:nth-child(2) {
	animation-delay: 0.65s;
}

.load1 view:nth-child(3) {
	animation-delay: 0.78s;
}

.load2 view:nth-child(3) {
	animation-delay: 0.91s;
}

.load3 view:nth-child(3) {
	animation-delay: 1.04s;
}

.load1 view:nth-child(4) {
	animation-delay: 1.17s;
}

.load2 view:nth-child(4) {
	animation-delay: 1.3s;
}

.load3 view:nth-child(4) {
	animation-delay: 1.43s;
}

@-webkit-keyframes load {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0.2;
	}
}
</style>
