<template>
	<view>
		<!-- 地址 -->
		<navigator url="/pages_set/address/address?source=1" class="address-section">
			<view class="order-content">
				<text class="yticon icon-shouhuodizhi"></text>
				<view class="cen" v-if="addressData">
					<view class="top">
						<text class="name">{{ addressData.name }}</text>
						<text class="mobile">{{ addressData.mobile }}</text>
					</view>
					<text class="address">{{ addressData.address }}</text>
				</view>
				<view class="cen" v-else>未选择地址</view>
				<text class="yticon icon-you"></text>
			</view>
		</navigator>

		<view class="goods-section">
			<!-- 商品列表 -->
			<view class="g-item">
				<image :src="detail.pic | jointPic"></image>
				<view class="right">
					<text class="title ">{{ detail.name }}</text>
					<text class="spec">{{ detail.orderStandard }}</text>
					<view class="price-box">
						<view class="price">￥{{ detail.price }}</view>
						<view class="number">x {{ detail.count }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部 -->
		<view class="footer">
			<view class="price-content">合计：￥{{ total }}</view>
			<text class="submit" @click="submit">提交订单</text>
		</view>
	</view>
</template>

<script>
	import { mapMutations } from 'vuex';
	export default {
		data() {
			return {
				maskState: 0, //消费券面板显示状态
				desc: '', //备注
				payType: 1, //1微信 2支付宝
				couponList: [
				{
					title: '新用户专享消费券',
					price: 5
				},
				{
					title: '庆五一发一波消费券',
					price: 10
				},
				{
					title: '消费券消费券消费券消费券',
					price: 15
				}],
				addressData: {},
				detail: this.$store.state.detail[0],
				isDeduction: 0,
				total: '',
				deductionTotal: '',
				fromPic: null
			};
		},
		onShow() {
			console.log(this.detail);
			this.getAddress();
			this.calcTotal();
		},
		methods: {
			getAddress() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt(
							{
								limit: 1,
								offset: 1,
								uid: uni.getStorageSync('userInfo').userId
							},
							true
						),
						a_m: 'address_uAddL'
					},
					success: res => {
						if (res.code == 2000) {
							this.addressData = res.data.rows[0];
						}
					}
				});
			},

			calcTotal() {
				let total = this.detail.price * this.detail.count;
				this.total = Number(total.toFixed(2));
			},

			avatarChoose(item) {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						that.imgUpload(res.tempFilePaths);
						const tempFilePaths = res.tempFilePaths;
					}
				});
			},
			imgUpload(file) {
				uni.uploadFile({
					url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
					filePath: file[0], // 图片本地路径
					name: 'file', // 固定
					success: res => {
						this.fromPic = JSON.parse(res.data).data[0].fileName;
					},
					fail: err => {}
				});
			},
			submit() {
				let that = this;
				uni.showModal({
					title: '提示',
					content: '确定购买此商品',
					success: function(res) {
						if (res.confirm) {
							that.http({
								data: {
									a_d: that.$rsa.encrypt(
										{
											userId: uni.getStorageSync('userInfo').userId,
											addId: that.addressData.id,
											mcId: that.detail.id,
											standard: that.detail.standard,
											tradeNumber: that.detail.count,
											voucher: that.fromPic
										},
										true
									),
									a_m: 'merchant_merchantOrder'
								},
								success: res => {
									if (res.code == 2000) {
										that.$api.msg('删除成功');
										uni.redirectTo({
											url: '/pages/money/paySuccess'
										});
									}
								}
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background: $page-color-base;
		padding-bottom: 100upx;
	}

	.address-section {
		.order-content {
			padding: 30upx 0;
			display: flex;
			align-items: center;
			background: #fff;
			width: 95vw;
			margin: 10px auto;
			border-radius: 10upx;
		}

		.icon-shouhuodizhi {
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90upx;
			color: #888;
			font-size: 44upx;
		}

		.cen {
			display: flex;
			flex-direction: column;
			flex: 1;
			font-size: 28upx;
			color: $font-color-dark;
		}

		.name {
			font-size: 34upx;
			margin-right: 24upx;
		}

		.address {
			margin-top: 16upx;
			margin-right: 20upx;
			color: $font-color-light;
		}

		.icon-you {
			font-size: 32upx;
			color: $font-color-light;
			margin-right: 30upx;
		}
	}

	.goods-section {
		background: #fff;
		width: 95vw;
		margin: 20upx auto;
		border-radius: 10upx;
		padding: 20upx 0;

		.g-header {
			display: flex;
			align-items: center;
			height: 84upx;
			padding: 0 30upx;
			position: relative;
		}

		.logo {
			display: block;
			width: 50upx;
			height: 50upx;
			border-radius: 100px;
		}

		.name {
			font-size: 30upx;
			color: $font-color-base;
			margin-left: 24upx;
		}

		.g-item {
			display: flex;
			margin: 10rpx 20upx;
			align-items: end;

			image {
				flex-shrink: 0;
				display: block;
				width: 140upx;
				height: 140upx;
				border-radius: 8upx;
			}

			.right {
				height: 140upx;
				flex: 1;
				padding-left: 24upx;
				overflow: hidden;
				position: relative;
			}

			.title {
				font-size: 30upx;
				font-weight: 600;
				color: $font-color-dark;
			}

			.spec {
				font-size: 26upx;
				color: $font-color-light;
			}

			.price-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
				justify-items: end;
				font-size: 32upx;
				color: $font-color-dark;
				padding-top: 10upx;
				position: absolute;
				bottom: 0px;
				width: 90%;

				.price {
					margin-bottom: 4upx;
				}

				.number {
					font-size: 26upx;
					color: $font-color-base;
					margin-left: 20upx;
				}
			}

			.step-box {
				position: relative;
			}
		}
	}

	.yt-list {
		width: 95vw;
		background: #fff;
		margin: 20upx auto;
		border-radius: 10upx;
		padding: 10upx 0;
	}

	.yt-list-cell {
		display: flex;
		padding: 0 40upx;
		line-height: 70upx;

		&.b-b:after {
			left: 30upx;
		}

		.cell-icon {
			height: 32upx;
			width: 32upx;
			font-size: 22upx;
			color: #fff;
			text-align: center;
			line-height: 32upx;
			background: #f85e52;
			border-radius: 4upx;
			margin-right: 12upx;

			&.hb {
				background: #ffaa0e;
			}

			&.lpk {
				background: #3ab54a;
			}
		}

		.cell-more {
			align-self: center;
			font-size: 24upx;
			color: $font-color-light;
			margin-left: 8upx;
			margin-right: -10upx;
		}

		.cell-tit {
			flex: 1;
			font-size: 26upx;
			// color: $font-color-light;
			margin-right: 10upx;
		}

		.cell-pic {
			width: 300rpx;
		}

		.cell-tip {
			font-size: 26upx;
			color: $font-color-dark;

			&.disabled {
				color: $font-color-light;
			}

			&.active {
				color: $base-color;
			}

			&.red {
				color: $base-color;
			}
		}

		&.desc-cell {
			.cell-tit {
				max-width: 90upx;
			}
		}

		.desc {
			flex: 1;
			font-size: $font-base;
			color: $font-color-dark;
		}
	}

	/* 支付列表 */
	.pay-list {
		padding-left: 40upx;
		margin-top: 16upx;
		background: #fff;

		.pay-item {
			display: flex;
			align-items: center;
			padding-right: 20upx;
			line-height: 1;
			height: 110upx;
			position: relative;
		}

		.icon-weixinzhifu {
			width: 80upx;
			font-size: 40upx;
			color: #6bcc03;
		}

		.icon-alipay {
			width: 80upx;
			font-size: 40upx;
			color: #06b4fd;
		}

		.icon-xuanzhong2 {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 60upx;
			height: 60upx;
			font-size: 40upx;
			color: $base-color;
		}

		.tit {
			font-size: 32upx;
			color: $font-color-dark;
			flex: 1;
		}
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 995;
		display: flex;
		align-items: center;
		width: 100%;
		height: 90upx;
		justify-content: space-between;
		font-size: 30upx;
		background-color: #fff;
		z-index: 998;
		color: $font-color-base;
		box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);

		.price-content {
			padding-left: 30upx;
			font-size: 36upx;
			color: $uni-color-primary;
		}

		.price-tip {
			color: $base-color;
			margin-left: 8upx;
		}

		.price {}

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 280upx;
			height: 100%;
			color: #fff;
			font-size: 32upx;
			background-color: $uni-color-primary;
		}
	}

	.pic-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto;

		// text-align: center;
		.pic-box-box {
			display: flex;
			justify-content: space-between;

			.imgAvatar {
				width: 40%;

				.iavatar {
					width: 100%;
					height: 200rpx;
					border-radius: 10rpx;
				}

				.iavatars {
					width: 100%;
					height: 200rpx;
				}
			}
		}

		.imgAvatars {
			width: 100%;

			.imgAvatars-iavatar {
				width: 100%;
			}

			.imgAvatars-iavatars {
				width: 200rpx;
			}
		}
	}

	.uifrom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20upx 0;
		font-size: 28upx;

		.uifrom-time {
			display: flex;
			align-items: center;
			width: 70%;

			text {
				padding: 0 30rpx;
			}
		}

		input {
			width: 70%;
			font-size: 28upx;
		}

		.uifrom-btn {
			background: #148744;
			color: #ffffff;
			font-size: 28rpx;
			border-radius: 5upx;
			padding: 6rpx 28rpx;
		}
	}
</style>