<template>
	<view class="page">
		<view class="from">
			<view class="from-title">请填写门店相关信息</view>
			<view class="from-item">
				<view>门店名称</view> <input type="text" placeholder="请输入商户招牌上的名称" v-model="from.merchantName" />
			</view>
			<view class="from-item">
				<view>分店名称</view> <input type="text" placeholder="选填，如万达广场店" v-model="from.branchName" />
			</view>
			<view class="from-item">
				<view>主营类目</view>
				<uni-data-select class="item-select" v-model="from.classifyId" :localdata="typeRang" @change="change" placeholder="请选择" :clear="false"></uni-data-select>
			</view>
			<view class="from-item">
				<view>门店位置</view>
				<view class="item-address" @click="addressShow = true">
					<text v-if="address">{{address}}</text> <text class="default" v-else>请选择</text>
					<uni-icons class="icon" type="bottom" size="14" color="#999" />
				</view>
			</view>
			<view class="from-item">
				<view>门店地址</view> <input type="text" placeholder="请输入楼号/楼层/门牌号等" v-model="from.storeAddress" />
			</view>
		</view>
		<view class="from">
			<view class="from-title">请填写企业或个体户相关信息</view>
			<view class="from-item">
				<view>公司名称</view> <input type="text" placeholder="请输入公司名称" v-model="from.corporateName" />
			</view>
			<view class="from-item">
				<view>法人姓名</view> <input type="text" placeholder="请输入法人姓名" v-model="from.userName" />
			</view>
			<view class="from-item">
				<view>法人手机号</view> <input type="text" placeholder="请输入法人手机号" v-model="from.userMobile" />
			</view>
			<view class="from-item">
				<view>法人身份证号</view> <input type="text" placeholder="请输入法人身份证号" v-model="from.idCard" />
			</view>
		</view>
		<view class="upload" v-if="isOnline == '0'">
			<view class="upload-title">身份证照片</view>
			<view class="upload-box">
				<view class="box-item" @tap="avatarChooses('idCardFront')">
					<image class="item-inp" v-if="from.idCardFront" :src="from.idCardFront | jointPic" mode="scaleToFill"></image>
					<image class="item-inp" v-else :src="`static/<EMAIL>` | jointPic" mode="scaleToFill"></image>
					<view class="item-text"> 身份证正面</view>
				</view>
				<view class="box-item" @tap="avatarChooses('idCardBack')">
					<image class="item-inp" v-if="from.idCardBack" :src="from.idCardBack | jointPic" mode="scaleToFill"></image>
					<image class="item-inp" v-else :src="`static/<EMAIL>` | jointPic" mode="scaleToFill"></image>
					<view class="item-text"> 身份证反面</view>
				</view>
				<view class="box-item" @tap="avatarChooses('idCardHold')">
					<image class="item-inp" v-if="from.idCardHold" :src="from.idCardHold | jointPic" mode="scaleToFill"></image>
					<image class="item-inp" v-else :src="`static/<EMAIL>` | jointPic" mode="scaleToFill"></image>
					<view class="item-text"> 手持身份证正面照</view>
				</view>
			</view>
		</view>
		<view class="upload">
			<view class="upload-title">请上传营业执照和门头照</view>
			<view class="upload-box">
				<view class="box-item" @tap="avatarChooses('businessLicenseFront')">
					<image class="item-inp" v-if="from.businessLicenseFront" :src="from.businessLicenseFront | jointPic" mode="scaleToFill"></image>
					<image class="item-inp" v-else :src="`static/<EMAIL>` | jointPic" mode="scaleToFill"></image>
					<view class="item-text"> 营业执照</view>
				</view>
				<view class="box-item" @tap="avatarChooses('storeLargePicture')">
					<image class="item-inp" v-if="from.storeLargePicture" :src="from.storeLargePicture | jointPic" mode="scaleToFill"></image>
					<image class="item-inp" v-else :src="`static/<EMAIL>` | jointPic" mode="scaleToFill"></image>
					<view class="item-text"> 门头照</view>
				</view>
			</view>
		</view>
		<view class="btn">
			<!-- <view class="btn-item" @tap="savaInfo()">提交审核</view> -->
			<view class="btn-item" v-if="isAudit == -1 " @tap="savaInfo()">提交审核</view>
			<view class="btn-item" v-if="isAudit == 2" @tap="savaInfo()">已拒绝 重新申请</view>
			<view class="btn-item no" v-if="isAudit == 0" @tap="savaInfo()">审核中</view>
		</view>
		<!-- <view class="uifrom-save-no" v-if="isAudit == 1"><button class="save">已 通 过</button></view> -->

		<pickerAddress v-model="addressShow" @confirm="addresspick" />
	</view>
</template>

<script>
	import pickerAddress from '@/components/liudx-pickerAddress/index.vue'
	export default {
		components: { pickerAddress },
		data() {
			return {
				from: {
					idCardFront: null, // 身份证正面
					idCardBack: null, // 身份证反面
					idCardHold: null, // 手持身份证正面照
					businessLicenseFront: null, // 营业执照
					storeLargePicture: null, // 门头照
					branchName: null, // 分店名称
					corporateName: null, // 公司名称
					merchantName: null, // 门店名称
					storeAddress: null, // 门店地址
					userMobile: null, // 法人手机号
					userName: null, // 法人姓名
					idCard: null, // 法人身份证号
					classifyId: null, // 类目分类id

					provincialCode: null, // 省级编码
					provincialName: null, // 省级名称

					cityCode: null, // 市级编码
					cityName: null, // 市级名称

					districtCode: null, // 区级编码
					districtName: null, // 区级名称
				},
				isAudit: null,
				addressShow: false,
				address: null,
				typeRang: [],
				isOnline: uni.getStorageSync('isOnline')
			};
		},
		onShow() {
			this.getData();
			this.getClass()
		},
		methods: {
			getData() {
				this.http({
					data: { a_m: "subscribersMerchant_merchant", a_d: this.$rsa.encrypt({ userId: uni.getStorageSync('userInfo').userId, merchantType: 1 }, true) },
					success: res => {
						if (res.code == 2000) {
							this.isAudit = res.data.isAudit
							if (this.isAudit == -1) { return }
							this.from = res.data
							this.address = `${res.data.province} ${res.data.city} ${res.data.district}`
						}
					}
				})
			},
			getClass() {
				this.http({
					url: '/api/bdc/ct',
					data: { a_m: "subscribersMerchant_classify", a_d: this.$rsa.encrypt({ userId: uni.getStorageSync('userInfo').userId }, true) },
					success: res => {
						let arr = res.data
						this.typeRang = res.data.map(item => ({
							text: item.classifyName,
							value: item.id
						}));
					}
				})
			},
			change(e) {
				this.from.classifyId = e;
			},

			savaInfo() {
				let u = { userId: uni.getStorageSync('userInfo').userId, applyType: 1 }
				this.from = { ...this.from, ...u }
				this.http({
					data: { a_m: "subscribersMerchant_apply", a_d: this.$rsa.encrypt(this.from, true) },
					success: res => { this.$api.msg(res.message); }
				})
			},

			// 地址选择
			addresspick(obj) {
				this.address = `${obj.province} ${obj.city} ${obj.district}`
				this.from = { ...this.from, ...obj }
			},



			avatarChooses(item) {
				console.log(item);

				if (!item) return
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						uni.uploadFile({
							url: 'https://boshi.channce.com/bj/fe/fileUpload', //接口地址
							filePath: res.tempFilePaths[0], // 图片本地路径
							name: 'file', // 固定
							success: res => {
								that.from[item] = JSON.parse(res.data).data[0].fileName;
							}
						});
					}
				});
			},
		}
	};
</script>

<style lang="scss">
	.page {
		background-color: #fff;
	}

	.from {
		padding-top: 20rpx;

		.from-title {
			padding: 20rpx 30rpx;
			font-size: 30rpx;
			color: #9B9B9B;
		}

		.from-item {
			display: flex;
			justify-content: space-between;
			padding: 0 30rpx;
			font-size: 26rpx;
			border-bottom: 1px solid rgba(230, 230, 230, 0.39);
			height: 40px;
			align-items: center;

			input {
				width: 70%;
				font-size: 26rpx;
				text-align: end;
			}

			.item-select {
				flex: none;
				width: 200rpx;
			}

			.item-address {
				font-size: 26rpx;

				.default {
					color: #9B9B9B;
				}

				.icon {
					margin-left: 10rpx;
				}

			}
		}
	}

	.upload {
		padding-top: 20rpx;
		border-bottom: 1px solid rgba(230, 230, 230, 0.39);

		.upload-title {
			padding: 0 30rpx;
			font-size: 30rpx;
			color: #9B9B9B;
		}


		.upload-box {
			padding: 15px 15px 10px;
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;

			.box-item {
				width: 320rpx;
				text-align: center;
				margin-bottom: 30rpx;

				.item-inp {
					width: 320rpx;
					height: 200rpx;
					border-radius: 15rpx;
					border: 1px solid rgba(230, 230, 230, 0.39);
				}

				.item-text {
					font-size: 26rpx;
					color: #434343;
					margin-top: 10rpx;
				}
			}

			.uploadItem-box {
				width: 50vw;
			}

		}


	}

	.btn {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx 0;

		.btn-item {
			width: 90vw;
			height: 90rpx;
			border-radius: 20rpx;
			background: #FF3836;
			font-size: 30rpx;
			color: #FFFFFF;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.no {
			background: #a7a7a7;
		}
	}
</style>