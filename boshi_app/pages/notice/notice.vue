<template>
	<view>
		<smh-nav class="nav" :list="slist" :current="defaultindex" @change="switchs"></smh-nav>
		<view class="notice-item" v-for="(item, index) in list" :key="index" v-if="list.length > 0">
			<view class="content" @click="navTo(item)">
				<text class="title">{{ item.title }}</text>
				<view class="img-wrapper">{{ item.createTime | formatDate}}</view>
				<view class="bot b-t">
					<!-- <text>{{item.subheading}}</text> -->
					<!-- <text v-html="item.content"></text> -->
					<!-- <web-view :src="item.content"></web-view> -->
				</view>
			</view>
		</view>
		<luanqing-empty :show="list.length == 0" text="没有更多数据啦" textColor="#000"></luanqing-empty>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				slist: [{
					name: '系统公告',
					id: 1
				}, {
					name: '平台广告',
					id: 2
				}, {
					name: '官方资讯',
					id: 3
				}],
				type: 1,
				defaultindex: 0
			};
		},
		onShow() {
			this.loadData();
		},
		methods: {
			switchs(e) {
				let index = e.index; //下标
				this.defaultindex = index;
				this.type = e.id; //id
				this.loadData();
			},
			loadData() {
				this.carouselList = [];
				this.http({
					url: '/api/usersMessage/list',
					method: 'post',
					data: {
						limit: 50,
						offset: 1,
						messageType: this.type
					},
					success: res => {
						this.list = res.data.rows;
					}
				});
			},
			navTo(item) {
				uni.navigateTo({
					url: `/pages/notice/noticeDetail?id=${item.id}&messageType=${item.messageType}`
				});
			},
		}
	};
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;
		padding-bottom: 30upx;
	}

	.nav {
		background-color: #ffffff;
	}

	.notice-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 30upx;
	}

	.time {
		display: flex;
		align-items: center;
		justify-content: center;
		padding-top: 10upx;
		font-size: 26upx;
		color: #7d7d7d;
	}

	.content {
		width: 710upx;
		padding: 0 24upx;
		background-color: #fff;
		border-radius: 4upx;
	}

	.title {
		display: flex;
		align-items: center;
		height: 90upx;
		font-size: 32upx;
		color: #303133;
	}

	.img-wrapper {
		width: 100%;
		font-size: 24rpx;
		color: #9ca0a6;
		position: relative;
		padding-bottom: 5px;
	}

	.pic {
		display: block;
		width: 100%;
		height: 100%;
		border-radius: 6upx;
	}

	.cover {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		font-size: 36upx;
		color: #fff;
	}

	.introduce {
		display: inline-block;
		padding: 16upx 0;
		font-size: 28upx;
		color: #606266;
		line-height: 38upx;
	}

	.bot {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0;
		font-size: 24upx;
		color: #7d7d7d;
		position: relative;
	}

	.more-icon {
		font-size: 32upx;
	}
</style>