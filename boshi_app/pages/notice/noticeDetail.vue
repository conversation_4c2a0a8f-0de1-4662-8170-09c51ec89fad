<template>
	<view class="notice-item">
		<view class="content" @click="navTo(item)">
			<text class="title">{{ detail.title }}</text>
			<view class="img-wrapper">{{ detail.createTime | formatDate}}</view>
			<view class="bot b-t">
				<text v-html="detail.content"></text>
				<!-- <web-view :src="item.content"></web-view> -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				detail: {}
			}
		},
		onLoad(option) {

			this.http({
				url: '/api/usersMessage/list',
				method: 'post',
				data: {
					limit: 1,
					offset: 1,
					messageType: option.messageType,
					id: option.id
				},
				success: res => {
					this.detail = res.data.rows[0];
				}
			});
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;
		padding-bottom: 30upx;
	}

	.nav {
		background-color: #ffffff;
	}

	.notice-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 30upx;
	}

	.time {
		display: flex;
		align-items: center;
		justify-content: center;
		padding-top: 10upx;
		font-size: 26upx;
		color: #7d7d7d;
	}

	.content {
		width: 710upx;
		padding: 0 24upx;
		background-color: #fff;
		border-radius: 4upx;
	}

	.title {
		display: flex;
		align-items: center;
		height: 90upx;
		font-size: 32upx;
		color: #303133;
	}

	.img-wrapper {
		width: 100%;
		font-size: 24rpx;
		color: #9ca0a6;
		position: relative;
		padding-bottom: 5px;
	}

	.pic {
		display: block;
		width: 100%;
		height: 100%;
		border-radius: 6upx;
	}

	.cover {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		font-size: 36upx;
		color: #fff;
	}

	.introduce {
		display: inline-block;
		padding: 16upx 0;
		font-size: 28upx;
		color: #606266;
		line-height: 38upx;
	}

	.bot {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0;
		font-size: 24upx;
		color: #7d7d7d;
		position: relative;
	}

	.more-icon {
		font-size: 32upx;
	}
</style>