<template>
	<view class="app">
		<view class="price-box">
			<text>支付余额</text>
			<text class="price">{{ detail.actualAmount }}</text>
		</view>

		<view class="pay-type-list">
			<radio-group @change="radioChange">
				<view class="" v-if="detail.plural != 1">
					<label class="type-item b-b">
						<text class="icon yticon icon-erjiye-yucunkuan"></text>
						<view class="con"> <text class="tit">积分支付</text> <text>可用积分：{{ cyBerIntegral }}</text> </view>
						<radio value="5" :checked="payWay == 5" />
					</label>
					<!-- <label class="type-item b-b">
						<text class="icon yticon icon-erjiye-yucunkuan"></text>
						<view class="con"> <text class="tit">余额支付</text> <text>可用余额：{{balance}}</text></view>
						<radio value="4" :checked="payWay == 4" />
					</label>
					<label class="type-item b-b">
						<text class="icon yticon icon-erjiye-yucunkuan"></text>
						<view class="con">
							<text class="tit">余额 + 积分支付</text>
							<text>可用余额：{{balance}}，可用积分：{{ cyBerIntegral }}</text>
						</view>
						<radio value="6" :checked="payWay == 6" />
					</label> -->
				</view>
				<!-- #ifndef MP-WEIXIN -->
				<!-- <label class="type-item b-b">
					<text class="icon yticon icon-alipay"></text>
					<view class="con"> <text class="tit">支付宝支付</text> </view>
					<radio value="10" :checked="payWay == 10" />
				</label> -->
				<!-- <label class="type-item b-b">
					<text class="icon yticon icon-weixinzhifu"></text>
					<view class="con"> <text class="tit">微信支付</text> </view>
					<radio value="13" />
				</label> -->
				<!-- <label class="type-item b-b">
					<text class="icon yticon icon-erjiye-yucunkuan"></text>
					<view class="con"> <text class="tit">银行卡支付</text> </view>
					<radio value="11" :checked="payWay == 11" />
				</label> -->
				<!-- #endif -->
				<!-- #ifdef MP-WEIXIN -->
				<label class="type-item b-b"  v-if="detail.plural == 1">
					<text class="icon yticon icon-weixinzhifu"></text>
					<view class="con"> <text class="tit">微信支付</text> </view>
					<radio value="2" :checked="payWay == 2" />
				</label>
				<!-- #endif -->
			</radio-group>
		</view>
		<view class="list-cell" @click="navTo('/pages_set/bank/bank')" :hover-stay-time="50" v-if="payWay == 11">
			<text class="cell-tit">银行卡管理</text>
			<text class="cell-more yticon icon-you"></text>
		</view>
		<view class="pay-type-list" v-if="payWay == 11">
			<radio-group @change="radioBankChange">
				<view class="type-item b-b" v-for="(item, index) in bankList" :key="index">
					<view class="con"> <text class="tit">{{ item.bankOpen }}</text> </view>
					<label class="radio">
						<radio :value="item.signNo" color="#fa436a" />
					</label>
				</view>
			</radio-group>
		</view>

		<text class="mix-btn" @click="confirmPay()">确认支付</text>

		<uni-popup ref="codeBox" type="bottom">
			<view class="popBox">
				<view class="popBox-input">
					<view class="popBox-input-title">短信验证码</view>
					<input type="password" v-model="confirmFrom.smsCode" placeholder="输入短信验证码" />
				</view>
				<button class="popBox-btn" @click="confirmBank()">確定付款</button>
			</view>
		</uni-popup>

		<uni-popup ref="passwordPop" type="bottom">
			<view class="popBox">
				<view class="popBox-input">
					<view class="popBox-input-title">支付密码</view>
					<input type="password" v-model="from.pa" placeholder="输入支付密码" />
				</view>
				<button class="popBox-btn" @click="confirm()">确定</button>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	// 简化版Base64编码，替代base-64库
	const Base64 = {
		encode: function(str) {
			try {
				return btoa(unescape(encodeURIComponent(str)));
			} catch (e) {
				console.error('Base64编码失败:', e);
				return str;
			}
		},
		decode: function(str) {
			try {
				return decodeURIComponent(escape(atob(str)));
			} catch (e) {
				console.error('Base64解码失败:', e);
				return str;
			}
		}
	};
	export default {
		data() {
			return {
				userId: uni.getStorageSync('userInfo').userId,
				// 支付类型 4:余额 5:积分 10:支付宝支付 11:银行卡支付
				payWay: 5,
				// 分区ID
				payId: null,
				// 是否是购物车
				pageType: "",
				// 商品详情
				detail: this.$store.state.order,

				balance: '',
				cyBerIntegral: '',
				code: '',
				bankType: 1,
				confirmFrom: {},
				from: {},

				istime: true,
				times: '',
				bankList: [],
				signNo: '',
				pageData: {}

			};
		},
		onShow() {
			const { referrerInfo } = uni.getEnterOptionsSync();
			this.getData();
		},
		onLoad(options) {
			// 路由获取商品分区ID
			this.payId = options.payId;
			// 是否是购物车
			this.pageType = options.type;
			console.log(this.detail.plural);
		},
		methods: {
			navTo(url) {
				uni.navigateTo({ url });
			},
			// 获取其他资金余额
			getData() {
				this.http({
					data: {
						a_m: 'cyBerBalance_anb',
						a_d: this.$rsa.encrypt({
							uid: uni.getStorageSync('userInfo').userId
						}, true)
					},
					success: res => {
						if (res.code == 2000) {
							this.balance = res.data;
						}
					}
				});
				this.http({
					data: {
						a_m: 'cyBerIntegral_anb',
						a_d: this.$rsa.encrypt({
							uid: uni.getStorageSync('userInfo').userId
						}, true)
					},
					success: res => {
						if (res.code == 2000) {
							this.cyBerIntegral = res.data;
						}
					}
				});
			},

			// 选择支付方式
			radioChange(event) {
				this.payWay = parseInt(event.detail.value);
				// 获取银行卡列表
				if (this.payWay == 11) {
					this.http({
						data: {
							a_d: this.$rsa.encrypt({
								limit: 20,
								offset: 1,
								uid: uni.getStorageSync('userInfo').userId
							}, true),
							a_m: 'subscribersBank_getBankCardNo'
						},
						success: res => {
							if (res.code == 2000) {
								this.bankList = res.data.rows
							}
						}
					});
				}
			},
			// 选择银行卡
			radioBankChange(event) {
				this.signNo = event.detail.value;
			},

			// 支付
			confirmPay() {
				// 其他资金支付弹出二级密码弹窗
				if (this.payWay == 4 || this.payWay == 5 || this.payWay == 6) {
					this.http({
						data: {
							a_m: "secondaryPwd_isHasPassword",
							a_d: this.$rsa.encrypt({
								userId: this.userId
							}, true)
						},
						success: res => {
							if (res.data) {
								this.$refs.passwordPop.open('bottom')
							} else {
								this.$api.msg('未设定交易密码')
								setTimeout(() => {
									uni.navigateTo({
										url: '/pages_set/security'
									})
								}, 500)
							}
						}
					})
					return
				}
				// 无银行卡提示
				if (this.payWay == 11 && this.bankList.length == 0) {
					this.$api.msg('暂无可用银行卡，请先签约银行卡')
					return
				}
				// 支付
				this.confirm()
			},

			//确认支付
			confirm() {
				switch (this.pageType) {
					case 'cart':
						this.cartPay()
						break;
					case 'buy':
						this.buyPay()
						break;
				}
			},

			// 购物车支付
			cartPay() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							addId: this.detail.addId,
							disCountWay: this.detail.disCountWay,
							pa: this.encryption(this.from.pa),
							payWay: this.payWay,
							signNo: this.signNo,
							uuid: this.detail.uuid,
							ggid: this.detail.ggid,
							userId: uni.getStorageSync('userInfo').userId
						}, true),
						a_m: 'cart_cartPay'
					},
					success: res => {
						// this.$api.msg(res.message);
						if (res.code == 2000) {
							this.$refs.passwordPop.close('bottom');
							this.payCallback(res)
						}
					}
				});
			},

			// 直接支付
			buyPay() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							addId: this.detail.addId,
							disCountWay: this.detail.disCountWay,
							id: this.detail.id,
							pa: this.encryption(this.from.pa),
							payWay: this.payWay,
							signNo: this.signNo,
							sizes: this.detail.sizes,
							model: this.detail.model,
							standard: this.detail.standard,
							tradeNumber: this.detail.count,
							userId: uni.getStorageSync('userInfo').userId
						}, true),
						a_m: 'so_cpPay'
					},
					success: res => {
						// this.$api.msg(res.message);
						if (res.code == 2000) {
							this.$refs.passwordPop.close('bottom');
							this.payCallback(res)
						}
					}
				});
			},

			// 支付回调处理
			payCallback(res) {
				switch (this.payWay) {
					case 10:
						// 支付宝 h5支付处理
						if (res.data.ra_Code == 100) {
							var reg = /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
							var urlStr = encodeURI(res.data.rc_Result.match(reg));
							// #ifdef H5 
							if (uni.getSystemInfoSync().platform == 'ios') {
								location.href = urlStr
							} else {
								uni.navigateTo({
									url: `/pages/webview/zfbPay?url=${urlStr}`
								})
							}
							// #endif
							// #ifdef APP-PLUS
							plus.runtime.openURL(urlStr);
							// #endif
							// setTimeout(() => { uni.redirectTo({ url: `/pages/money/paySuccess` }); }, 10000);
						} else {
							this.$api.msg(res.data.rb_CodeMsg);
							return;
						}
						break;
					case 13:
						// 微信 h5支付处理
						if (res.data.ra_Code == 100) {
							// var reg = /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
							var urlStr = JSON.parse(res.data.rc_Result).openlink;
							// #ifdef H5
							location.href = urlStr
							// #endif
							// #ifdef APP-PLUS
							plus.runtime.openURL(urlStr);
							// #endif
							// setTimeout(() => { uni.redirectTo({ url: `/pages/money/paySuccess` }); }, 2000);
						} else {
							this.$api.msg(res.data.rb_CodeMsg);
							return;
						}
						break;
					case 2:
						this.wxPayCallBack(res)
						break;
					case 11:
						// 银行卡支付处理
						this.$refs.codeBox.open('bottom');
						this.confirmFrom.orderNo = res.data;
						this.getCode()
						break;
					default:
						// 其他资金支付处理
						setTimeout(() => {
							uni.redirectTo({
								url: `/pages/money/paySuccess`
							});
						}, 2000);
						break;
				}
			},

			wxPayCallBack(res) {
				let appid = 'wx7e9427822b2dc3b0'
				let packages = `prepay_id=${res.data.prepay_id}`
				let nonceStr = res.data.nonce_str
				let prepay_id = res.data.prepay_id
				let timeStamp = parseInt(new Date().getTime() / 1000) + ''

				this.http({
					url: '/api/wxPay/rps',
					method: 'post',
					data: {
						appid: appid,
						nonceStr: nonceStr,
						pack: packages,
						timestamp: timeStamp
					},
					success: res => {
						// 调用微信支付接口
						uni.requestPayment({
							timeStamp: timeStamp, // 时间戳
							nonceStr: nonceStr, // 随机字符串
							package: packages, // 订单详情扩展字符串
							signType: 'MD5', // 签名类型
							paySign: res.data, // 签名
							success: function(res) {
								setTimeout(() => {
									uni.redirectTo({
										url: `/pages/money/paySuccess`
									});
								}, 2000);
							},
							fail: function(err) {
								console.error('Payment failed:', err); // 支付失败回调
							}
						});
					}
				});
			},

			// 银行卡确认支付
			confirmBank() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							adddata1: this.confirmFrom.adddata1,
							signNo: this.signNo,
							orderNo: this.confirmFrom.orderNo,
							userId: uni.getStorageSync('userInfo').userId,
							smsCode: this.confirmFrom.smsCode,
						}, true),
						a_m: 'so_joinBankPay'
					},
					success: res => {
						if (res.code == 2000) {
							let data = JSON.parse(res.data.data);
							if (data.order_status == 'P1000') {
								setTimeout(() => {
									uni.redirectTo({
										url: `/pages/money/paySuccess`
									});
								}, 2000);
							} else {
								this.$api.msg(data.err_msg);
								setTimeout(() => {
									this.$refs.popup.close();
								}, 1000);
							}
						}
					}
				});
			},

			// 银行卡支付获取验证码
			getCode() {
				this.http({
					url: '/api/sendVCode/se',
					method: 'post',
					data: {
						uid: uni.getStorageSync('userInfo').userId,
						bankName: this.bankFrom.bankName,
						bankNo: this.bankFrom.bankNo,
						bankMobile: this.bankFrom.bankMobile,
						idNo: this.bankFrom.idNo
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.times = 60;
							this.istime = false;
							this.timer = setInterval(() => {
								this.times--;
								if (this.times === 0) {
									clearInterval(this.timer);
									this.istime = true;
								}
							}, 1000);
						}
					}
				});
			},

			encryption(item) {
				if (item) {
					// base64加密密码
					var en = Base64.encode(item);
					// rsa加密
					var app = getApp();
					const encryptor = new this.$jsencrypt();
					const publicKey = uni.getStorageSync('token');
					encryptor.setPublicKey(publicKey);
					return encryptor.encrypt(en);
				} else {
					return "";
				}
			},
		}
	};
</script>

<style lang="scss">
	.app {
		width: 100%;
		padding-bottom: 100px;
	}

	.price-box {
		background-color: #fff;
		height: 265upx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		font-size: 28upx;
		color: #909399;

		.price {
			font-size: 50upx;
			color: #303133;
			margin-top: 12upx;
		}
	}

	.pay-type-list {
		margin-top: 20upx;
		background-color: #fff;
		padding-left: 60upx;

		.type-item {
			height: 120upx;
			padding: 20upx 0;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-right: 60upx;
			font-size: 30upx;
			position: relative;
		}

		.icon {
			width: 100upx;
			font-size: 52upx;
		}

		.icon-erjiye-yucunkuan {
			color: #fe8e2e;
		}

		.icon-weixinzhifu {
			color: #36cb59;
		}

		.icon-alipay {
			color: #01aaef;
		}

		.tit {
			font-size: $font-lg;
			color: $font-color-dark;
			margin-bottom: 4upx;
		}

		.con {
			flex: 1;
			display: flex;
			flex-direction: column;
			font-size: $font-sm;
			color: $font-color-light;
		}

		.con-box {
			border: 1px solid #fe8e2e;
			width: fit-content;
			padding: 4rpx 10rpx;
			border-radius: 10rpx;
		}
	}

	.mix-btn {
		position: fixed;
		bottom: 10px;
		left: 50%;
		transform: translate(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 630upx;
		height: 80upx;
		font-size: $font-lg;
		color: #fff;
		background-color: $uni-color-primary;
		border-radius: 100upx;
		box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);
	}

	.list-cell {
		display: flex;
		align-items: baseline;
		padding: 20upx $page-row-spacing;
		line-height: 60upx;
		position: relative;
		background: #fff;
		justify-content: center;
		margin-top: 10px;

		.cell-more {
			align-self: baseline;
			font-size: $font-lg;
			color: $font-color-light;
			margin-left: 10upx;
		}

		.cell-tit {
			flex: 1;
			font-size: $font-base + 2upx;
			color: $font-color-dark;
			margin-right: 10upx;
		}

		switch {
			transform: translateX(16upx) scale(0.84);
		}
	}
</style>