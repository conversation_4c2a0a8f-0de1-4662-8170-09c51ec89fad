<template>
	<view class="container">
		<!-- <view class="ui-list">
			<view>公众号：</view>
			<view class="">美满嘉圆</view>
		</view>
		<view class="ui-list">
			<view>小程序：</view>
			<view class="">美满嘉圆</view>
		</view>
		<view class="ui-list">
			<view>邮箱：</view>
			<view class=""><EMAIL></view>
		</view> -->
		<!-- <view class="ui-list">
			<view>网址：</view>
			<view class="">www.nanfangzhenxuan.cn</view>
		</view> -->
		<!-- <view class="ui-list">
			<view>地址：</view>
			<view class="">杭州市滨江区西兴街道滨康路228号3幢C座392室</view>
		</view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},
		onShow() {},
		methods: {}
	};
</script>

<style lang="scss">
	.ui-list {
		padding: 20rpx;
		border-bottom: solid 1px #e3e3e3;
		display: flex;

		:nth-child(1) {
			width: 20%;
			color: #030303;
			font-size: 30rpx;
		}

		:nth-child(2) {
			width: 80%;
			color: #030303;
			font-size: 30rpx;
		}

		text {
			color: #4a4a4a;
			font-size: 28rpx;
			display: inline-block;
			vertical-align: middle;
			min-width: 150rpx;
		}

		.place {
			color: #999999;
			font-size: 28rpx;
		}
	}
</style>