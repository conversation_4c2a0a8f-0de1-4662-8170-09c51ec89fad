<template>
	<view class="page">
		<text class="yticon icon-zuo back" @click="navigateBack()"></text>
		<view class="page-title">
			<image src="https://boshi.channce.com/imgs/yxstatic/share/wenan.png" mode="widthFix"></image>
		</view>
		<view class="centers">
			<image class="centers-bg" src="https://boshi.channce.com/imgs/yxstatic/share/zhuBg.png" mode="widthFix"></image>
			<view class="code-c centerX">
				<!-- #ifdef MP-WEIXIN -->
				<image class="qrcode" :src="wxAppletQrCode | jointPic" mode="widthFix"></image>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<QRCode ref="qrcode" />
				<!-- #endif -->
				<view class=""> 邀请码: {{userInfo.inviteCode}} </view>
			</view>
			<image class="btn centerX" @click="copy()" src="https://boshi.channce.com/imgs/yxstatic/share/btn.png" mode="widthFix"></image>
			<image class="liucheng centerX" src="https://boshi.channce.com/imgs/yxstatic/share/liucheng.png" mode="widthFix"></image>
		</view>
	</view>
</template>

<script>
	import QRCode from "@/components/qr_code/qrcode.vue"
	export default {
		components: {
			QRCode
		},
		data() {
			return {
				qrUrl: "",
				qrSize: 180,
				userInfo: "",
				wxAppletQrCode: ""
			}
		},
		onReady() {
			// #ifdef H5
			this.canvasCreate() //该代码仅在h5中生效
			// #endif
		},
		onShow() {
			this.userInfo = uni.getStorageSync('userInfo')
			this.qrUrl = `https://boshi.channce.com/boshi_w/#/pages_account/regists?code=${this.userInfo.inviteCode}`
			// #ifdef MP-WEIXIN
			this.getData()
			// #endif
		},
		mounted() {},
		methods: {
			getData() {
				this.http({
					url: '/api/subscribers/basicsInfo',
					data: { uid: this.userInfo.userId },
					success: res => { this.wxAppletQrCode = res.data.wxAppletQrCode; }
				});
			},
			navigateBack() {
				uni.navigateBack()
			},
			// 创建二维码
			canvasCreate() {
				let _this = this;
				_this.$refs.qrcode.make({
						text: _this.qrUrl
					})
					.then(res => {
						// 返回的res与uni.canvasToTempFilePath返回一致
						// console.log(res)
						_this.qrCode = res.tempFilePath;
						_this.onCanvas();
					});
			},
			copy(value) {
				uni.setClipboardData({
					data: value,
					success: () => {
						this.$api.msg("复制成功");
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	.page {
		background-image: url(https://boshi.channce.com/imgs/yxstatic/share/<EMAIL>);
		background-size: 100% 100%;
		height: 100vh;
		overflow: hidden;
		position: relative;
		padding-top: 140rpx;


		.back {
			position: absolute;
			top: 80rpx;
			left: 40rpx;
			color: #ffffff;
			font-weight: 800;
			font-size: 40rpx;
		}

		.page-title {
			width: 80vw;
			margin: 0 auto;
			padding-top: 20rpx;
			color: #ffffff;
			position: absolute;
			left: 50%;
			transform: translate(-50%);

			image {
				width: 100%;
			}
		}

		.centers {
			position: relative;
			width: 100vw;

			.centers-bg {
				width: 100%;
				margin: 0 auto;
				display: block;
			}

			.code-c {
				position: absolute;
				top: 17%;
				text-align: center;
				color: #410000;
				font-size: 16px;

				view {
					padding-top: 20rpx;
				}


				.qrcode {
					display: block;
					border-radius: 5px;
					padding: 5px;
					border: 1px solid #8B0000;
				}

				image {
					width: 180px;
				}

			}


			.btn {
				position: absolute;
				bottom: 38%;
				text-align: center;
				width: 60vw;
			}

			.liucheng {
				position: absolute;
				bottom: 25%;
				text-align: center;
				width: 60vw;
			}


			.qr {
				width: 400rpx;
				height: 400rpx;
				margin-bottom: 44rpx;
			}
		}

		.centerX {
			left: 52%;
			transform: translateX(-50%);
		}

	}
</style>