<template>
	<view class="page">
		<view class="title">
			文案欢迎咨询铂时客服，商户入驻，积分规则，订单问题，推荐客户均可联系我们
		</view>
		<text>工作时间 周二至周日9:00-12:00 13:00-17:00</text>
		<image :src="'/yxcs/cs1.png' | jointPic" mode="widthFix"></image>
		<image :src="'/yxcs/cs2.png' | jointPic" mode="widthFix"></image>
	</view>
</template>

<script>
	import HorizontalSelect from "@/components/horizontal-select.vue"
	export default {
		components: {
			HorizontalSelect
		},
		data() {
			return {
				userId: uni.getStorageSync('userInfo').userId,
				selectOption: {
					selectList: [],
					selectId: 1,
				},
				pageData: {}
			};
		},
		onShow() {

			this.getData()
		},
		methods: {
			getData() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ userId: this.userId }, false), a_m: 'level_levelConfig' },
					success: res => {
						this.selectOption.selectList = res.data
						this.pageData = res.data[0]
					}
				});
			},
			eventClick(item) {
				this.selectOption.selectId = item.id
				this.pageData = item
			},
			buy() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ level: this.selectOption.selectId, userId: this.userId }, true), a_m: 'level_levelOrder' },
					success: res => {
						that.$api.msg(res.message);
						if (res.code == 2000) {}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.page {
		padding-top: 15px;
		background-color: #fff;

		.title {
			display: block;
			width: 90vw;
			margin: 0 auto;
		}

		image {
			display: block;
			width: 80vw;
			margin: 20px auto;
		}

		text {
			display: block;
			width: 90vw;
			margin: 20px auto;
			font-size: 14px;
		}
	}
</style>