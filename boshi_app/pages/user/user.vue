<template>
	<view class="page">
		<!-- 设置 -->
		<view class="set">
			<!-- <uni-icons type="scan" size="20" @click="scanCode"></uni-icons> -->
			<image class="set-icon" :src="'yxStatic/icon/erweima.png' | jointPic" mode="widthFix" @click="scanCode"></image>
			<text class="" @click="navTo('/pages_set/set')">设置</text>
		</view>
		<!-- 用户信息 -->
		<view class="user">
			<image class="user-portrait" :src="userInfos.userPic | jointPic" mode="aspectFill"></image>
			<view class="user-info" v-if="isLogin">
				<view class="info-acc">
					<view class="acc-acc" v-if="userInfos.nickname == ''">{{ userInfos.mobile | retainTailNumber}}</view>
					<view class="acc-acc" v-if="userInfos.isMerchant != ''">{{ userInfos.nickname }}</view>
					<view class="acc-tag"> <text>会员ID：{{ userInfos.inviteCode }}</text> </view>
				</view>
				<view class="info-vip" @click="copy(userInfos.inviteCode)" v-if="userInfos.superMember != 0">
					<image class="vip-icon" :src="`/static/icon/vip.png` | jointPic" v-if="userInfos.superMember == 2" mode="heightFix" />
					<image class="vip-icon" :src="`/static/icon/vip1.png` | jointPic" v-if="userInfos.superMember == 1" mode="heightFix" />
					<view>{{userInfos.superMemberName}}</view>
				</view>
			</view>
			<!-- #ifndef MP-WEIXIN -->
			<view class="user-login" v-if="!isLogin" @click="navTo('/pages_account/login')">请登录/注册</view>
			<!-- #endif -->
			<!-- #ifdef MP-WEIXIN -->
			<view class="user-login" v-if="!isLogin" @click="navTo('/pages_account/wx_login')">请登录/注册</view>
			<!-- #endif -->
		</view>
		<!-- 钱包 -->
		<view class="asset" v-if="isLogin">
			<view class="asset-tit">
				<view class="asset-tit-con">我的钱包</view>
				<!-- <view class="asset-tit-btn"> 进入钱包 </view> -->
			</view>
			<view class="asset-list">
				<view class="list-item" v-for="item in asset" :key="item.index" @click="navTo(`/pages/user/capital/balance?kindId=${item.id}`)">
					<view class="item-num">{{ item.money }}</view>
					<view class="item-tit">{{ item.name }} <uni-icons type="right" size="10" /></view>
				</view>
			</view>
		</view>

		<view class="page-nav" v-if="isLogin">
			<view class="pageNav-tit">商城订单</view>
			<view class="pageNav-mod">
				<view class="pageNavMod-item" v-for="(item,index) in orderList" :key="index" @click="navTo(`/pages/order/order?state=${item.id}`)">
					<image class="yticon" :src="item.icon | jointPic" mode="widthFix"></image>
					<view>{{ item.title }}</view>
				</view>
			</view>
		</view>
		<!-- 		<view class="page-bannar">
			<image src="/yxstatic/bannar/<EMAIL>" @click="navTo('pages/userMall/userMall')" mode="widthFix"></image>
		</view> -->

		<view class="page-nav">
			<view class="pageNav-tit">分享拓展</view>
			<view class="pageNav-mod">
				<view class="pageNavMod-item" v-for="(item,index) in navList" :key="index" @click="navTo(item.path)">
					<image class="yticon" :src="item.icon | jointPic" mode="widthFix"></image>
					<view>{{ item.title }}</view>
				</view>
				<view class="pageNavMod-item" v-if="isOnline == '0'" @click="navTo('/pages_redPacket/sending')">
					<image class="yticon" :src="'/yxstatic/icon/icon (4).png' | jointPic" mode="widthFix"></image>
					<view>红包转账</view>
				</view>
				<view class="pageNavMod-item" v-if="isOnline == '0'" @click="navTo('/pages/user/realName')">
					<image class="yticon" :src="'/yxstatic/icon/icon (3).png' | jointPic" mode="widthFix"></image>
					<view>实名认证</view>
				</view>
			</view>
		</view>

		<view class="page-nav" v-if="userInfos.member == 1">
			<view class="pageNav-tit">服务商中心</view>
			<view class="pageNav-mod">
				<view class="pageNavMod-item" v-for="(item,index) in service" :key="index" @click="navTo(item.path)">
					<image class="yticon" :src="item.icon | jointPic" mode="widthFix"></image>
					<view>{{ item.title }}</view>
				</view>
			</view>
		</view>
		<view class="page-nav" v-if="userInfos.member == 2">
			<view class="pageNav-tit">商户中心</view>
			<view class="pageNav-mod">
				<view class="pageNavMod-item" v-for="(item,index) in merchant" :key="index" @click="navTo(item.path)">
					<image class="yticon" :src="item.icon | jointPic" mode="widthFix"></image>
					<view>{{ item.title }}</view>
				</view>
			</view>
		</view>
		<view class="page-nav" v-if="userInfos.member == 3">
			<view class="pageNav-tit">中介中心</view>
			<view class="pageNav-mod">
				<view class="pageNavMod-item" v-for="(item,index) in intermediary" :key="index" @click="navTo(item.path)">
					<image class="yticon" :src="item.icon | jointPic" mode="widthFix"></image>
					<view>{{ item.title }}</view>
				</view>
			</view>
		</view>


		<view class="page-fun">
			<view class="pageFun-item" @click="navTo('/pages/user/level')">
				<image class="pageFunItem-icon yticon" :src="'/yxstatic/icon/gnIcon (2).png' | jointPic" mode="widthFix"></image>
				<text class="pageFunItem-tit">联系我们 <text>（周二至周日 9:00-17:00）</text> </text>
				<text class="pageFunItem-more yticon icon-you"></text>
			</view>
			<view class="pageFun-item" v-if="isOnline == '0'" @click="navTo('/pages/userMall/powerAudit')">
				<image class="pageFunItem-icon yticon" :src="'/yxstatic/icon/gnIcon (5).png' | jointPic" mode="widthFix"></image>
				<text class="pageFunItem-tit">商户申请</text>
				<text class="pageFunItem-more yticon icon-you"></text>
			</view>
		</view>
		<view class="page-beian">Copyright @ 2023-2025 粤ICP备2023109904号-1</view>
		<view class="page-LogOut" v-if="isLogin" @click="toLogout">退出登录</view>

		<uni-popup ref="popup" type="center">
			<view class="QRcodeBox">
				<view class="QRcodeBox-image">
					<image :src="QRcode | jointPic" mode="widthFix"></image>
				</view>
				<image class="btn" :src="'/yxStatic/<EMAIL>' | jointPic" mode="widthFix" @click="$refs.popup.close()"></image>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import { mapState, mapMutations } from 'vuex';
	export default {
		data() {
			return {
				userInfos: {
					userPic: 'yxStatic/missing-face.png',
				},
				asset: [],
				orderList: [
					{ id: -1, title: '全部订单', path: '/pages/user/friend', icon: '/yxstatic/icon/orderIcon (4).png' },
					{ id: 1, title: '待发货', path: '/pages/user/share', icon: '/yxstatic/icon/orderIcon (2).png' },
					{ id: 2, title: '待收货', path: '/pages_set/security', icon: '/yxstatic/icon/orderIcon (3).png' },
					{ id: 4, title: '已完成', path: '/pages_set/set', icon: '/yxstatic/icon/orderIcon (1).png' },
				],
				navList: [
					{ title: '我的团队', path: '/pages/user/friend', icon: '/yxstatic/icon/icon (2).png' },
					{ title: '分享海报', path: '/pages/user/share', icon: '/yxstatic/icon/icon (1).png' },
					{ title: '设置管理', path: '/pages_set/set', icon: '/yxstatic/icon/icon (4).png' },

				],
				funList: [
					{ title: '', path: '/pages/user/level', icon: '/yxstatic/icon/gnIcon (2).png' },
					// { title: '商户申请', path: '/pages/userMall/powerAudit', icon: '/yxstatic/icon/gnIcon (5).png' },
				],
				service: [
					{ title: '商品管理', path: '../../pages_serviceProvider/goodsList', icon: '/yxstatic/icon/icon (5).png' },
					{ title: '店铺信息', path: '../../pages_serviceProvider/storeManagement', icon: '/yxstatic/icon/icon (7).png' },
					{ title: '订单管理', path: '../../pages_serviceProvider/storeOrder', icon: '/yxstatic/icon/icon (6).png' },
					{ title: '核销码', path: '../../pages_serviceProvider/storeManagement', icon: '/yxstatic/icon/icon (7).png' },
				],
				merchant: [
					{ title: '商品管理', path: '../../pages_merchant/goodsList', icon: '/yxstatic/icon/icon (5).png' },
					{ title: '订单管理', path: '../../pages_merchant/storeOrder', icon: '/yxstatic/icon/icon (6).png' },
				],
				intermediary: [
					{ title: '租房管理', path: '../../pages_intermediary/goodsList?type=1', icon: '/yxstatic/icon/icon (5).png' },
					{ title: '卖房管理', path: '../../pages_intermediary/goodsList?type=2', icon: '/yxstatic/icon/icon (5).png' },
					{ title: '订单管理', path: '../../pages_intermediary/order', icon: '/yxstatic/icon/icon (6).png' },
				],
				QRcode: '',
				isOnline: uni.getStorageSync('isOnline')
			};
		},
		onShow() {
			if (this.isLogin) {
				this.getData();
				this.getNum()
			}
		},
		computed: {
			...mapState(['isLogin', 'userInfo'])
		},
		methods: {
			...mapMutations(['logout']),

			getData() {
				this.http({
					url: '/api/subscribers/basicsInfo',
					data: { uid: this.userInfo.userId },
					success: res => {
						this.userInfos = res.data;
						if (res.data.userPic == '') { this.userInfos.userPic = 'yxStatic/applogo.png' }
					}
				});
			},

			getNum() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ u: this.userInfo.userId, home: 1 }, true), a_m: 'da_allMoney' },
					success: res => { if (res.code == 2000) { this.asset = res.data; } }
				});
			},

			copy(value) {
				uni.setClipboardData({ data: value, success: () => { this.$api.msg('复制成功'); } });
			},

			scanCode() {
				this.http({
					url: '/api/bdm/gmqc',
					data: { userId: this.userInfo.userId },
					success: res => {
						this.QRcode = res.data;
						this.$refs.popup.open();
					}
				});
			},

			navTo(url) {
				uni.navigateTo({ url });
				if (url == 'pages/userMall/userMall') {
					if (userInfos.superMember <= 3) {
						return
					}
				}
				if (!uni.getStorageSync('token')) {
					url = '/pages_account/login';
				}
			},

			//退出登录
			toLogout() {
				uni.showModal({
					content: '确定要退出登录么',
					success: e => {
						if (e.confirm) {
							this.logout();
							setTimeout(() => {
								// #ifndef MP-WEIXIN
								setTimeout(() => { uni.reLaunch({ url: `/pages_account/login` }) }, 200)
								// #endif
								// #ifdef MP-WEIXIN 
								setTimeout(() => { uni.navigateTo({ url: `/pages_account/wx_login` }) }, 200)
								// #endif
							}, 200);
						}
					}
				});
			},

		}
	};
</script>
<style lang="scss">
	.page {
		padding-top: 100rpx;
		padding-bottom: 40rpx;
		background-image: url('https://boshi.channce.com/imgs/yxstatic/bg/background.png');
		background-size: 100%;
		background-repeat: no-repeat;
		//  #ifndef MP-WEIXIN
		min-height: calc(100vh - 50px);
		//  #endif 

		//  #ifdef MP-WEIXIN
		min-height: 100vh;
		//  #endif 


		.set {
			font-size: 26rpx;
			color: #000000;
			width: 90%;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			padding-top: 60rpx;


			text {
				margin-left: 20rpx;
			}

			.set-icon {
				width: 40rpx;
			}
		}


		.user {
			border-radius: 10rpx;
			padding: 30rpx;
			padding-right: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.user-portrait {
				height: 100rpx;
				width: 100rpx;
				border-radius: 50%;
			}

			.user-info {
				width: calc(100% - 120rpx);
				display: flex;
				justify-content: space-between;
				align-items: center;

				.info-acc {
					.acc-acc {
						font-size: 40rpx;
						color: #000000;
						margin-bottom: 10rpx;
					}

					.acc-tag {
						font-size: 22rpx;
						color: #434343;

						text {
							border-radius: 6rpx;
							border: 1px solid #434343;
							margin-right: 10rpx;
						}
					}
				}

				.info-vip {
					font-size: 24rpx;
					color: #6D6D6D;
					display: flex;
					align-items: center;
					background: rgba(255, 255, 255, 0.16);
					border-radius: 100px 0px 0px 100px;
					border: 1px solid #FFFFFF;
					padding: 8rpx 10rpx 8rpx 20rpx;

					.vip-icon {
						height: 30rpx;
					}

					:nth-child(2) {
						font-size: 20rpx;
						color: #4A5371;
						margin-left: 10rpx;
					}
				}
			}

			.user-login {
				width: calc(100% - 130rpx);
				font-size: 46rpx;
				font-weight: 600;
			}

		}

		.asset {
			background: #ffffff;
			width: 95vw;
			margin: 30rpx auto 0;
			border-radius: 10rpx;
			padding: 30upx;

			.asset-tit {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.asset-tit-con {
					font-size: 32rpx;
				}

				.asset-tit-btn {
					font-size: 12px;
					color: #9B9B9B;
				}
			}

			.asset-list {
				display: flex;

				.list-item {
					margin: 40rpx 0 0;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					font-size: 28rpx;
					width: 25%;
					position: relative;

					.item-num {
						font-size: 40rpx;
						margin-bottom: 4px;
						font-weight: 600;
						color: #000000;
					}

					.item-tit {
						text-align: center;
						padding: 2px 0;
						font-size: 24rpx;
					}
				}
			}


		}


		.page-bannar {
			width: 95vw;
			margin: 40rpx auto;

			image {
				width: 100%;
				height: 100%;
				border-radius: 5px;
			}
		}

		.page-nav {
			background: #ffffff;
			width: 95vw;
			margin: 10px auto 0;
			border-radius: 20rpx;
			padding: 30upx;
			// box-shadow: 0px 2px 6px 0px #00000040;

			.pageNav-tit {
				padding-bottom: 30upx;
			}

			.pageNav-mod {
				display: flex;
				justify-content: flex-start;
				align-content: center;
				flex-wrap: wrap;

				.pageNavMod-item {
					width: 25%;
					border-radius: 10upx;
					font-size: 28rpx;
					text-align: center;
					margin: 10rpx 0;

					image {
						width: 50rpx;
					}
				}

				.icon-shouhoutuikuan {
					font-size: 44upx;
				}
			}
		}

		.page-fun {
			background: #ffffff;
			width: 95vw;
			margin: 10px auto 0;
			border-radius: 20rpx;
			padding: 30upx;

			.pageFun-item {
				display: flex;
				align-items: baseline;
				padding: 10px 0;
				line-height: 31px;
				position: relative;
				border-bottom: 1px solid #ebebeb;

				.pageFunItem-icon {
					align-self: center;
					width: 29px;
					max-height: 31px;
					font-size: 19px;
				}

				.pageFunItem-tit {
					flex: 1;
					font-size: 14px;
					color: #303133;
					margin-right: 5px;

					text {
						font-size: 12px;
					}
				}

				.pageFunItem-more {
					align-self: center;
					font-size: 15px;
					color: #606266;
					margin-left: 10px;
				}

				image {
					width: 45%;
				}
			}

		}

		.page-beian {
			background: #ffffff;
			width: 95vw;
			margin: 10px auto 0;
			border-radius: 20rpx;
			padding: 30upx;
			text-align: center;
			font-size: 12px;
		}

		.page-LogOut {
			display: flex;
			align-items: center;
			margin: 40rpx auto;
			justify-content: center;
			width: 95vw;
			height: 80upx;
			font-size: 32upx;
			color: #DE2848;
			background: #FFFFFF;
			border-radius: 100rpx;
		}
	}

	.QRcodeBox {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 90vw;
		z-index: 999;
		text-align: center;

		.QRcodeBox-image {
			padding: 40rpx;
			border-radius: 10rpx;
			background: rgba(0, 0, 0, .5);

			image {
				width: 70%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.btn {
			width: 40px;
			margin-top: 60rpx;

		}
	}
</style>