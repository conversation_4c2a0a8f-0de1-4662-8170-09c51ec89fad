<template>
	<view class="friend">
		<view class="top">
			<view class="top-li">
				<view class="num">{{ teamInfos.directNumber }}</view>
				<view>推荐人数</view>
			</view>
			<view class="top-li">
				<view class="num">{{ teamInfos.directAchievement }}</view>
				<view>推荐业绩</view>
			</view>
		</view>
		<scroll-view class="friend-list" style="height:65vh;" scroll-y="true" lower-threshold="30" @scrolltolower="scrollLower" show-scrollbar="false">
			<view class="friend-boxs" title="列表文字" note="列表描述信息" v-for="item in list" :key="item.index">
				<view class="friend-boxs-top">
					<image class="friend-boxs-top-img" src="https://boshi.channce.com/imgs/yxstatic/missing-face.png" mode="widthFix"></image>
					<view class="friend-boxs-top-user">
						<view class="friend-boxs-top-user-top"> {{ item.nickname}} <text v-if="item.member != 0">{{ item.memberLevel }}</text> </view>
						<view class="friend-boxs-top-user-time">{{ item.createTime | formatDate }}</view>
					</view>
					<!-- <view class="friend-boxs-top-btn" @click="update(item)">查看业绩数据</view> -->
				</view>
				<view class="friend-box" v-if="item.userId == showTeam">
					<view class="friend-box-item">
						<view>团队业绩</view>
						<view class="num">{{ itemData.teamAchievement }}</view>
					</view>
					<view class="friend-box-item">
						<view>小市场业绩</view>
						<view class="num">{{ itemData.minTeamAchievement }}</view>
					</view>
					<view class="friend-box-item">
						<view>直推人数</view>
						<view class="num">{{ itemData.directNumber }}</view>
					</view>
					<view class="friend-box-item">
						<view>团队有效人数</view>
						<view class="num">{{ itemData.teamEffectiveNumber }}</view>
					</view>
				</view>
			</view>
			<view v-if="list.length == 0" class="empty">
				<image src="/static/emptyCart.png" mode="aspectFit"></image>
				<view class="empty-tips">暂无数据</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		components: {},
		data() {
			return {
				tabCurrentIndex: 0,
				list: [],
				page: 1,
				teamInfos: {},
				triggered: '',
				itemData: {},
				showTeam: '',
				typelist: [{
					name: '直推',
					id: 1
				}, {
					name: '接点',
					id: 3
				}, ],
				value: 1,
			};
		},

		onLoad() {
			this.getData();
			this.loadData();
		},
		methods: {
			//获取订单列表
			getData() {
				this.http({
					url: '/api/subscribers/loginInfo',
					method: 'post',
					data: {
						uid: uni.getStorageSync('userInfo').userId
					},
					success: res => {
						this.teamInfos = res.data;
					}
				});
			},
			changeType(item) {
				this.value = item.id;
				this.page = 1
				this.list = []
				this.loadData();
			},
			loadData() {
				this.http({
					url: '/api/subscribers/teamInfo',
					method: 'post',
					data: {
						limit: 10,
						offset: this.page,
						type: this.value,
						uid: uni.getStorageSync('userInfo').userId
					},
					success: res => {
						for (let i = 0; i < res.data.rows.length; i++) {
							this.list.push(res.data.rows[i]);
						}
						this.allTotal = res.data.total;
					}
				});
			},
			update(item) {
				this.showTeam = item.userId;
				this.http({
					url: '/api/subscribers/teamAchievement',
					method: 'post',
					data: {
						uid: item.userId
					},
					success: res => {
						this.itemData = res.data;
					}
				});
			},
			scrollLower() {
				if (this.list.length >= this.allTotal) {
					this.status = 'noMore';
					this.$api.msg('当前已加载完毕');
					return;
				} else {
					this.status = 'loading';
					this.page++;
					this.loadData();
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	page {
		background-color: #ffffff;
	}

	.friend {
		font-size: 14px;
		padding-top: 40rpx;

		.top {
			padding: 40rpx;
			color: #ffffff;
			width: 90vw;
			margin: 0 auto 0;
			border-radius: 5px;
			background-image: linear-gradient(to right, #4897f6, #8a3bff);
			display: flex;
			justify-content: space-between;

			.top-li {
				width: 50%;
				text-align: center;

				.num {
					font-size: 38rpx;
					font-weight: 600;
				}

				:nth-child(2) {
					font-size: 24rpx;
					color: #f1e2e0;
				}
			}

		}

		.title {
			width: 100vw;
			background-color: #ffffff;
			padding: 15px;
			font-weight: 600;
			font-size: 30upx;
		}

		.selectTop-box {
			width: 90vw;
			margin: 20rpx auto;
			overflow: auto;

			.selectTop-select {
				display: flex;
				align-items: center;
				position: relative;

				view {
					// width: 50%;
					font-size: 24rpx;
					color: #9F9F9F;
					text-decoration: none;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 40rpx;
					height: 40rpx;
					padding: 0 20rpx;
				}

				.is_li {
					cursor: pointer;
					color: #ffffff;
					background: #FFDCE8;
					border-radius: 16px;
					color: #C80348;
				}

			}
		}

		.friend-list {
			width: 90vw;
			margin: 0 auto 0;
			padding-top: 40rpx;

			.friend-boxs {
				box-sizing: border-box;
				padding: 20px 10px 10px;
				border-bottom: 1px solid #f0f0f0;

				.friend-boxs-top {
					display: flex;
					align-items: center;

					.friend-boxs-top-img {
						width: 90rpx;
						box-shadow: 0px 0px 2px 2px #cacaca;
						border-radius: 50%;
					}

					.friend-boxs-top-user {
						margin-left: 20rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						align-items: flex-start;

						.friend-boxs-top-user-top {
							text {
								background-color: #f94e43;
								color: #ffffff;
								padding: 0 16rpx;
								font-size: 24rpx;
								border-radius: 10px;
								margin-left: 20rpx;
							}

						}

						.friend-boxs-top-user-time {
							padding-top: 20rpx;
						}
					}

					.friend-boxs-top-btn {
						background-color: #492623;
						height: 56rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						padding: 0 16rpx;
						border-radius: 14rpx;
						margin-left: 20rpx;
						color: #e5e0e0;
					}
				}

				.friend-box {
					display: flex;
					justify-content: flex-start;
					text-align: center;
					margin: 30rpx auto 0;
					padding: 10px 0;
					flex-wrap: wrap;
					border: 1px solid #dcdcdc;
					border-radius: 5px;

					.friend-box-item {
						width: 33.3%;
						border-radius: 10px;
						padding: 10px 0;

						.num {
							padding-bottom: 10rpx;
							font-size: 35rpx;
							color: #a53437;
						}

						:nth-child(1) {
							font-size: 24rpx;
							margin-bottom: 10rpx;
						}
					}
				}

				.right {
					color: #bababa;
					text-align: end;

					.vip {
						color: #f94e43;
					}
				}
			}
		}
	}

	.empty {
		width: 100%;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;

		image {
			width: 340upx;
			margin-bottom: 30upx;
		}

		.empty-tips {
			display: flex;
			font-size: $font-sm + 2upx;
			color: $font-color-disabled;

			.navigator {
				color: $uni-color-primary;
				margin-left: 16upx;
			}
		}
	}
</style>
<style>
	.van-progress__portion {
		width: 141px;
	}
</style>