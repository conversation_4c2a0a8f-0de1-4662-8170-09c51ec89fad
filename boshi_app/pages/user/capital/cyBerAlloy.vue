<template>
	<view class="page">
		<view class="page-asset">
			<view class="page-asset-box">
				<view class="page-asset-box-left">
					<view>合金</view>
					<view>{{ balance.cyBerAlloy }}</view>
				</view>
				<view class="page-asset-box-right">
					<view @click="$refs.combinationPop.open('bottom')">组合</view>
				</view>
			</view>
		</view>
		<view class="scroll">
			<scroll-view class="scroll-list" style="height:75vh;" scroll-y="true" lower-threshold="30" @scrolltolower="scrollLower" show-scrollbar="false">
				<view v-for="(item, index) in list" :key="index" class="order-item" v-if="list.length > 0">
					<view class="left">
						<view class="text">{{ item.context }}</view>
						<view class="time">{{ item.time | formatDate }}</view>
					</view>
					<view class="right">
						<text v-if="item.status == 0" class="red">- {{ item.money }}</text>
						<text v-else class="green">+ {{ item.money }}</text>
					</view>
				</view>
				<luanqing-empty :show="list.length == 0" text="没有更多数据啦" textColor="#000"></luanqing-empty>
			</scroll-view>
		</view>
		<uni-popup ref="combinationPop" type="bottom">
			<view class="popBoxs">
				<view class="popBox-title">合成积分</view>
				<view class="popBox-num">
					<view class="">余额：{{ balance.cyBerBalance | number_format }}</view>
					<view class="">合金：{{ balance.cyBerAlloy | number_format }}</view>
				</view>
				<view class="popBox-input">
					<view class="popBox-input-title">组合数量</view>
					<view class="popBox-input-box">
						<view class="popBox-input-box-item">
							<input type="number" placeholder="请输入组合数量" v-model="from.money" @input="input()" />
							<text>余额</text>
						</view>
						<view class="">+</view>
						<view class="popBox-input-box-item">
							<view class="">{{from.money1}}</view>
							<text>合金</text>
						</view>
					</view>
				</view>
				<view class="popBox-input">
					<view class="popBox-input-title">支付密码</view>
					<input class="popBox-input-item" type="password" placeholder="请输入支付密码" v-model="from.password" />
				</view>
				<button class="popBox-btn" @click="combination()">确定</button>
			</view>
		</uni-popup>

	</view>
</template>

<script>
	import Base64 from 'base-64';
	export default {
		data() {
			return {
				list: [],
				page: 1,
				balance: {
					cyBerBalance: 0,
					cyBerAlloy: 0,
				},
				from: {},
				userInfos: {}
			};
		},
		onShow() {
			this.list = [];
			this.loadData();
			this.getData();
		},
		methods: {
			getData() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ uid: uni.getStorageSync('userInfo').userId }, true), a_m: 'cyBerBalance_anb' },
					success: res => { this.balance.cyBerBalance = res.data; }
				});
				this.http({
					data: { a_d: this.$rsa.encrypt({ uid: uni.getStorageSync('userInfo').userId }, true), a_m: 'cyBerAlloy_anb' },
					success: res => { this.balance.cyBerAlloy = res.data; }
				});
				this.http({
					url: '/api/subscribers/basicsInfo',
					method: 'post',
					data: { uid: uni.getStorageSync('userInfo').userId },
					success: res => { this.userInfos = res.data; }
				});
			},
			loadData() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							uid: uni.getStorageSync('userInfo').userId,
							offset: this.page,
							limit: 10
						}, true),
						a_m: 'cyBerAlloy_bna'
					},
					success: res => {
						for (let i = 0; i < res.data.rows.length; i++) {
							this.list.push(res.data.rows[i]);
						}
						this.allTotal = res.data.total;
					}
				});
			},
			scrollLower() {
				if (this.list.length >= this.allTotal) {
					this.status = 'noMore';
					this.$api.msg('当前已加载完毕');
					return;
				} else {
					this.status = 'loading';
					this.page++;
					this.loadData();
				}
			},
			input() {
				this.from.money1 = (this.from.money * this.userInfos.customs).toFixed(2)
			},
			combination() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							m: this.from.money,
							pa: this.encryption(this.from.password),
							uid: uni.getStorageSync('userInfo').userId
						}, true),
						a_m: "cyBerAlloy_combination"
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.$refs.combinationPop.close();
							this.reLoad()
						}
					}
				});
			},

			encryption(item) {
				// base64加密密码
				var en = Base64.encode(item);
				// rsa加密
				var app = getApp();
				const encryptor = new this.$jsencrypt();
				const publicKey = uni.getStorageSync('token');
				encryptor.setPublicKey(publicKey);
				return encryptor.encrypt(en);
			},
			reLoad() {
				setTimeout(() => {
					this.from = {};
					this.loadData();
					this.getData();
				}, 1000);
			}
		}
	};
</script>

<style lang="scss">
	.page {
		.page-asset {
			position: relative;
			width: 100vw;
			background-color: #e0feca;

			.page-asset-box {
				padding: 15px;

				.page-asset-box-left {
					:nth-child(1) {
						font-size: 20upx;
					}

					:nth-child(2) {
						padding-top: 20upx;
						font-size: 54upx;
						font-weight: 600;
					}
				}

				.page-asset-box-right {
					display: flex;
					justify-content: flex-end;
					align-items: flex-end;
					color: #ffffff;

					view {
						height: 25px;
						background: $primary-btn-color;
						font-size: 24upx;
						text-align: center;
						line-height: 25px;
						border-radius: 5px;
						margin-left: 30rpx;
						padding: 0 20rpx;
					}
				}
			}

			.page-asset-nav {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				font-size: 14px;
				color: #555555;
				z-index: 3;
			}
		}

		.scroll {
			width: 95vw;
			margin: 40upx auto;

			.scroll-list {
				.order-item {
					background: #e0feca;
					margin-bottom: 20upx;
					padding: 20upx;
					border-radius: 5px;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left {
						.text {
							font-size: 30upx;
							padding-bottom: 5px;
							font-weight: bold;
						}

						.time {
							color: #a8a7a7;
							font-size: 24rpx;
						}
					}

					.right {
						padding-bottom: 20upx;
						font-weight: 600;

						.red {
							color: #dd524d;
						}

						.green {
							color: #4cd964;
						}
					}
				}
			}
		}

		.popBoxs {
			background-color: #f4f4f4;
			width: 100vw;
			padding: 40rpx;
			border-radius: 10upx 10upx 0 0;

			.popBox-title {
				font-size: 20px;
				padding-bottom: 10px;
				font-weight: bold;
			}

			.popBox-num {
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.popBox-input {
				font-size: 30upx;

				.popBox-input-title {
					padding-bottom: 10px;
					padding-top: 20px;
				}

				.popBox-input-box {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.popBox-input-box-item {
						border-radius: 5px;
						height: 39px;
						width: 45%;
						color: #101010;
						font-size: 14px;
						border: 1px solid #bbb;
						box-sizing: border-box;
						background-color: #fff;
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding-right: 20rpx;
						padding: 0 20rpx;

						input {
							width: 70%;
						}
					}
				}

				.popBox-input-item {
					border-radius: 5px;
					height: 76rpx;
					color: #101010;
					font-size: 14px;
					border: 1px solid #bbb;
					box-sizing: border-box;
					padding: 0 15px;
					background-color: #fff;
				}
			}

			.popBox-input-text {
				font-size: 12px;
				padding-top: 10rpx;
			}

			.popBox-btn {
				margin: 30px 0 0;
				color: #fff;
				font-weight: 600;
				border: none;
				height: 36px;
				border-radius: 5px;
				font-size: 15px;
				background: $primary-btn-color;
				width: 100%;
			}
		}


	}
</style>