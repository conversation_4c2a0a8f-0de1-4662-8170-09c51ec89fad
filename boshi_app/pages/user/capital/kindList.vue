<template>
	<view class="mainbox">
		<view v-for="item in kindList" :key="item.index" class="item" @click="navTo(item)">
			<image v-if="item.id == 6" class="icon" src="https://boshi.channce.com/imgs/yxstatic/icon/dou (1).png" mode="widthFix"></image>
			<image v-if="item.id == 7" class="icon" src="https://boshi.channce.com/imgs/yxstatic/icon/dou (2).png" mode="widthFix"></image>
			<image v-if="item.id == 8" class="icon" src="https://boshi.channce.com/imgs/yxstatic/icon/dou (3).png" mode="widthFix"></image>
			<image v-if="item.id == 9" class="icon" src="https://boshi.channce.com/imgs/yxstatic/icon/dou (4).png" mode="widthFix"></image>
			<image v-if="item.id == 10" class="icon" src="https://boshi.channce.com/imgs/yxstatic/icon/dou (5).png" mode="widthFix"></image>
			<view class="text">{{ item.name }}</view>
			<view class="num">剩余：{{ item.money }}</view>
			<text class="pageNav1-item-more yticon icon-you"></text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userId: uni.getStorageSync('userInfo').userId,
				kindList: [],
				type: ""
			};
		},
		onShow() {
			this.getData()
		},
		methods: {
			getData() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ u: this.userId, home: 0 }, true), a_m: 'da_allMoney' },
					success: res => { if (res.code == 2000) { this.kindList = res.data; } }
				});
			},
			navTo(item) {
				if (item.id == 9) {
					uni.navigateTo({ url: `/pages/tokens/tokens` });
					return
				}
				uni.navigateTo({ url: `/pages/user/capital/balance?kindId=${item.id}` });
			},
		}
	}
</script>

<style lang="scss" scoped>
	.mainbox {
		padding: 0 15px;
		font-size: 14px;

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #ccc;
			height: 50px;
			line-height: 50px;

			.icon {
				width: 30px;
				height: 30px;
			}

			.num {
				font-size: 12px;
			}

			.text {
				flex: 1;
				padding-left: 10px;
				width: 100px;
			}
		}

	}
</style>