<template>
	<view class="friend">
		<view class="top">
			<view class="top-li">
				<view class="num">{{ data.v1 }}</view>
				<view>消费商</view>
			</view>
			<view class="top-li">
				<view class="num">{{ data.v2 }}</view>
				<view>一星</view>
			</view>
			<view class="top-li">
				<view class="num">{{ data.v3 }}</view>
				<view>二星</view>
			</view>
			<view class="top-li">
				<view class="num">{{ data.v4 }}</view>
				<view>三星</view>
			</view>
			<view class="top-li">
				<view class="num">{{ data.v5 }}</view>
				<view>经理</view>
			</view>
			<view class="top-li">
				<view class="num">{{ data.v6 }}</view>
				<view>董事</view>
			</view>
			<view class="top-li">
				<view class="num">{{ data.v7 }}</view>
				<view>合伙人</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {},
		data() {
			return {
				data: 0,
				list: [],
				page: 1,
				data: {},
				triggered: '',
				itemData: {},
				showTeam: '',
				typelist: [{
					name: '直推',
					id: 1
				}, {
					name: '接点',
					id: 3
				}, ],
				value: 1,
			};
		},

		onLoad() {
			this.getData();
		},
		methods: {
			//获取订单列表
			getData() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt('{}', false),
						a_m: 'fc_fundPool',
					},
					success: res => {
						this.data = res.data
					}
				});
			},
		}
	};
</script>

<style lang="scss" scoped>
	page {
		background-color: #ffffff;
	}

	.friend {
		font-size: 14px;
		padding-top: 10rpx;

		.top {
			padding: 40rpx;
			color: #ffffff;
			width: 95%;
			margin: 0 auto 0;
			border-radius: 10px;
			background-image: linear-gradient(to right, #ff5353, #fe7f52);

			display: flex;
			justify-content: flex-start;
			text-align: center;
			margin: 40rpx auto 0;
			flex-wrap: wrap;


			.top-li {
				width: 33.3%;
				border-radius: 10px;
				margin-top: 30rpx;

				.num {
					padding-bottom: 10rpx;
					font-size: 40rpx;
					font-weight: 600;
				}

				:nth-child(2) {
					font-size: 24rpx;
					color: #f1e2e0;
				}
			}
		}

	}
</style>