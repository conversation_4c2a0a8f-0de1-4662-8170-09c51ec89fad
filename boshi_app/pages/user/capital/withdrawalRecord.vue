<template>
	<view class="page">
		<!-- <view class="record-list-title">提现票记录：</view> -->
		<!-- 订单列表 -->
		<scroll-view class="record-list" scroll-y @scrolltolower="scrollLower">
			<view class="record-item" v-for="item in list" :key="item.index">
				<view class="record-item-text">
					<view class="">状态：</view>
					<view class="" v-if="item.status == 0">审核中</view>
					<view class="" v-else-if="item.status == 1">提现成功</view>
					<view class="" v-else>拒绝提现</view>
				</view>
				<view class="record-item-text">
					<view class="">提现时间：</view>
					<view class="">{{ item.createTime | formatDate }}</view>
				</view>
				<view class="record-item-text">
					<view class="">提现金额：</view>
					<view class="">{{ item.withdrawMoney }}</view>
				</view>
				<view class="record-item-text">
					<view class="">手续费：</view>
					<view class="">{{ item.deductMoney }}</view>
				</view>
				<view class="record-item-text">
					<view class="">实到金额：</view>
					<view class="">{{ item.receivedMoney }}</view>
				</view>
				<view class="record-item-text" v-if="item.status == 2">
					<view class="">拒绝原因：</view>
					<view class="">{{ item.statusRemarks }}</view>
				</view>
			</view>
		</scroll-view>
		<luanqing-empty :show="list.length == 0" text="没有更多数据啦" textColor="#000"></luanqing-empty>
	</view>
</template>
<script>
import Base64 from 'base-64';
export default {
	components: {},
	data() {
		return {
			type: '',
			listUrl: '',
			tabCurrentIndex: 0,
			list: [],
			page: 1,
			triggered: false,
			balance: '',
			scrollTop: 0
		};
	},
	onShow() {
		this.list = [];
		this.loadData();
	},
	methods: {
		loadData() {
			this.http({
				data: {
					a_d: this.$rsa.encrypt(
						{
							i: 1,
							u: uni.getStorageSync('userInfo').userId,
							// u: '5087f339efb24f1591f3322f1da13849',
							offset: this.page,
							limit: 10
						},
						true
					),
					a_m: 'da_withdrawList'
				},
				success: res => {
					for (let i = 0; i < res.data.rows.length; i++) {
						this.list.push(res.data.rows[i]);
					}
					this.allTotal = res.data.total;
				}
			});
		},
		scrollLower() {
			if (this.list.length >= this.allTotal) {
				this.status = 'noMore';
				this.$api.msg('当前已加载完毕');
				return;
			} else {
				this.status = 'loading';
				this.page++;
				this.loadData();
			}
		},
		navTo(url) {
			uni.navigateTo({
				url
			});
		}
	}
};
</script>

<style lang="scss">
.page {
	padding-bottom: 300rpx;

	.record-list-title {
		color: #101010;
		font-size: 18px;
		width: 90vw;
		margin: 20rpx auto;
	}

	.record-list {
		.record-item {
			width: 90vw;
			margin: 20rpx auto;
			border-radius: 10px;
			background-color: rgba(255, 255, 255, 1);
			box-shadow: 3px 3px 6px 0px rgba(171, 168, 168, 88);
			padding: 10rpx 20px;

			.record-item-text {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				padding: 10rpx 0;

				:nth-child(1) {
					color: #7c909d;
					font-size: 14px;
					width: 35%;
				}

				:nth-child(2) {
					font-size: 14px;
					width: 60%;
				}
			}
		}
	}

	/* 底部操作菜单 */
	.page-bottom {
		position: fixed;
		bottom: 0px;
		z-index: 95;
		width: 100vw;
		background: #ffffff;
		box-shadow: 0 0 20upx 0 #aba8a850;
		box-sizing: border-box;
		padding: 40rpx;

		.page-bottom-input {
			display: flex;
			justify-content: space-between;

			image {
				width: 30px;
			}

			input {
				width: 85%;
				height: 30px;
				color: #888888;
				font-size: 14px;
				text-align: left;
				font-family: Arial;
				border: 1px solid #bbbbbb;
				padding: 0 20rpx;
			}
		}

		.action-btn-group {
			background-color: #ec4113;
			width: 100%;
			height: 90rpx;
			color: #ffffff;
			border-radius: 4px;
			text-align: center;
			line-height: 90rpx;
			margin-top: 40rpx;
		}
	}
}
</style>
