<template>
	<view class="page">
		<view class="page-asset">
			<view class="page-asset-box">
				<view class="page-asset-box-left">
					<view>{{ fundData.name }}</view>
					<view>{{ fundData.money }}</view>
				</view>
				<view class="page-asset-box-right">
					<view v-if="fundData.id == 1" @click="navTo('/pages/user/capital/withdrawal',fundData.id)">提现</view>
					<view v-if="fundData.id == 1" @click="navTo('/pages/user/capital/recharge',fundData.id)">充值</view>
					<!-- <view v-if="fundData.id == 1||fundData.id == 2||fundData.id == 3" @click="$refs.zhuanzhangPop.open('bottom')">转账</view> -->
					<view v-if="fundData.id == 2" @click="$refs.exchangePop.open('bottom')">兑换</view>
				</view>
			</view>
			<view class="page-asset-nav" v-if="listUrl == 'cyBerBalance_bna'" @click="navTo('/pages/user/capital/withdrawalRecord')">提现记录</view>
		</view>
		<view class="scroll">
			<scroll-view class="scroll-list" style="height:75vh;" scroll-y="true" lower-threshold="30" @scrolltolower="scrollLower" show-scrollbar="false">
				<view v-for="(item, index) in list" :key="index" class="order-item" v-if="list.length > 0">
					<view class="left">
						<view class="text">{{ item.context }}</view>
						<view class="time">{{ item.time | formatDate }}</view>
					</view>
					<view class="right">
						<text v-if="item.status == 0" class="red">- {{ item.money }}</text>
						<text v-else class="green">+ {{ item.money }}</text>
					</view>
				</view>
				<luanqing-empty :show="list.length == 0" text="没有更多数据啦" textColor="#000"></luanqing-empty>
			</scroll-view>
		</view>
		<uni-popup ref="zhuanzhangPop" type="bottom">
			<view class="popBox">
				<view class="popBox-title">转账</view>
				<view class="popBox-input">
					<view class="popBox-input-title">转账数量</view>
					<input type="number" placeholder="请输入转账数量" v-model="from.money" />
				</view>
				<view class="popBox-input">
					<view class="popBox-input-title">接收用户名</view>
					<input type="text" placeholder="请输入接收用户名" v-model="from.ac" />
				</view>
				<view class="popBox-input">
					<view class="popBox-input-title">支付密码</view>
					<input type="password" placeholder="请输入支付密码" v-model="from.password" />
				</view>
				<!-- <view class="popBox-input-text">注:转账扣除2%STZ</view> -->
				<button class="popBox-btn" @click="zhuanZhang()">确定</button>
			</view>
		</uni-popup>
		<uni-popup ref="exchangePop" type="bottom">
			<view class="popBox">
				<HorizontalSelect :selectOption="selectOption" @eventClick="changeSelect"></HorizontalSelect>
				<view class="popBox-input">
					<view class="popBox-input-title">兑换数量</view>
					<input type="number" placeholder="请输入兑换数量" v-model="from.money" />
				</view>
				<button class="popBox-btn" @click="exchange()">确定</button>
			</view>
		</uni-popup>

	</view>
</template>

<script>
	import Base64 from 'base-64';
	import HorizontalSelect from "@/components/horizontal-select.vue"
	export default {
		components: {
			HorizontalSelect
		},
		data() {
			return {
				userId: uni.getStorageSync('userInfo').userId,
				fundData: {},
				type: '',
				listUrl: '',
				tabCurrentIndex: 0,
				list: [],
				page: 1,
				balance: '',
				money: '',
				account: '',
				pic: '',
				from: {},
				transferPrice: 0,
				num2: 0,
				current: 0,
				kId: null,

				selectOption: {
					selectList: [{
						title: "CNY",
						id: 1
					}],
					selectId: 1,
					type: "text",
					textColor: "#fdd344"
				},
				exchangeId: 1
			};
		},
		onLoad(options) {
			this.kindId = options.kindId;
		},
		onShow() {
			this.list = [];
			this.loadData();
			this.getData();
		},
		methods: {
			changeSelect(data) {
				this.exchangeId = data
			},
			getData() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							u: this.userId,
							i: this.kindId
						}, true),
						a_m: 'da_allMoney'
					},
					success: res => {
						this.fundData = res.data[0];
					}
				});
			},
			loadData() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							u: this.userId,
							offset: this.page,
							limit: 10,
							i: this.kindId
						}, true),
						a_m: 'da_moneyList'
					},
					success: res => {
						for (let i = 0; i < res.data.rows.length; i++) {
							this.list.push(res.data.rows[i]);
						}
						this.allTotal = res.data.total;
					}
				});
			},
			scrollLower() {
				if (this.list.length >= this.allTotal) {
					this.status = 'noMore';
					this.$api.msg('当前已加载完毕');
					return;
				} else {
					this.status = 'loading';
					this.page++;
					this.loadData();
				}
			},
			navTo(url, item) {
				if (item) {
					uni.navigateTo({
						url: `${url}?id=${item}`
					});
				} else {
					uni.navigateTo({
						url
					});
				}
			},
			// 划转
			huaZhuan() {
				let datas;
				switch (this.type) {
					case 'cyBerBalance_anb':
						datas = {
							receive: 5,
							send: 1,
							money: this.from.money,
							u: this.userId
						};
						break;
					case 'cyBerAward_anb':
						datas = {
							receive: 1,
							send: 7,
							money: this.from.money,
							u: this.userId
						};
						break;
				}
				this.http({
					data: {
						a_d: this.$rsa.encrypt(datas, true),
						a_m: 'fc_deal'
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.reLoad()
							this.$refs.huaZhuanPop.close();
						}
					}
				});
			},
			// 确认转账
			zhuanZhang() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							kindId: this.kindId,
							money: this.from.money,
							site: this.from.ac,
							pa: this.encryption(this.from.password),
							userId: this.userId
						}, true),
						a_m: "da_tra"
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.reLoad()
							this.$refs.zhuanzhangPop.close();
						}
					}
				});
			},
			duiHuanYuE() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							money: this.from.money,
							receive: '1',
							send: this.kindId,
							u: this.userId
						}, true),
						a_m: "fc_deal"
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.reLoad()
							this.$refs.duiHuanYuE.close();
						}
					}
				});
			},
			duiHuanXfq() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							money: this.from.money,
							receive: '5',
							send: this.kindId,
							u: this.userId
						}, true),
						a_m: "fc_deal"
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.reLoad()
							this.$refs.duiHuanYuE.close();
						}
					}
				});
			},

			exchange() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							money: this.from.money,
							receive: this.kindId,
							send: this.exchangeId,
							u: this.userId
						}, true),
						a_m: "fc_deal"
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							this.reLoad()
							this.$refs.exchangePop.close();
						}
					}
				});

			},

			encryption(item) {
				// base64加密密码
				var en = Base64.encode(item);
				// rsa加密
				var app = getApp();
				const encryptor = new this.$jsencrypt();
				const publicKey = uni.getStorageSync('token');
				encryptor.setPublicKey(publicKey);
				return encryptor.encrypt(en);
			},
			reLoad() {
				setTimeout(() => {
					this.page = 1
					this.list = [];
					this.from = {};
					this.loadData();
					this.getData();
				}, 1000);
			}
		}
	};
</script>

<style lang="scss">
	.page {
		background: #ffffff;

		.page-asset {
			position: relative;
			width: 100vw;
			background-color: $base-color;

			.page-asset-box {
				padding: 15px;
				color: #ffffff;

				.page-asset-box-left {
					:nth-child(1) {
						font-size: 20upx;
					}

					:nth-child(2) {
						padding-top: 20upx;
						font-size: 54upx;
						font-weight: 600;
					}
				}

				.page-asset-box-right {
					display: flex;
					justify-content: flex-end;
					align-items: flex-end;

					view {
						height: 25px;
						background: #fff;
						color: $base-color;
						font-size: 24upx;
						text-align: center;
						line-height: 25px;
						border-radius: 5px;
						margin-left: 30rpx;
						padding: 0 20rpx;
					}
				}
			}

			.page-asset-nav {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				font-size: 14px;
				color: #555555;
				z-index: 3;
			}
		}

		.scroll {
			width: 95vw;
			margin: 40upx auto 0;

			.scroll-list {
				height: 79vh;

				.order-item {
					background: #ffffff;
					margin-bottom: 20upx;
					padding: 20upx;
					border-radius: 5px;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left {
						.text {
							font-size: 30upx;
							padding-bottom: 5px;
							font-weight: bold;
						}

						.time {
							color: #a8a7a7;
							font-size: 24rpx;
						}
					}

					.right {
						padding-bottom: 20upx;
						font-weight: 600;

						.red {
							color: #dd524d;
						}

						.green {
							color: #4cd964;
						}
					}
				}
			}
		}

	}
</style>