<template>
	<view class="page">
		<view class="page-box">
			<view class="text"> {{ pageData.name }} </view>
			<view class="text"> {{ pageData.money }} </view>
		</view>
		<view class="from">
			<view class="type">
				<view v-for="item in typelist" :key="item.index" :class="typeValue === item.id ? 'is_li' : ''" @click="changeType(item)"> {{ item.name }} </view>
			</view>
			<view class="from-box" v-if="!onLineType">
				<view class="tips">银行卡审核充值，只支持30000元及以上的金额。</view>
				<view class="tips">充值金额</view>
				<view class="input-text">充值前先实名认证</view>
				<view class="input-box">
					<input class="input number" type="number" @input="bankInput" v-model="rechargeFrom.money" placeholder="输入将要充值的数量">
				</view>
				<view v-if="bankShow">
					<view class="tips" v-if="typeValue == 1">银行卡号</view>
					<view class="input-code" v-if="typeValue == 1">
						<view id="code" class="code-input">{{dalink.addressGathering}}</view>
						<view class="btn-copy ads-copy" @click="copy(dalink.addressGathering)" data-clipboard-action="copy" data-clipboard-target="#code" alt="">复制</view>
					</view>
					<view class="tips" v-if="typeValue == 1">银行卡开户行</view>
					<view class="input-code" v-if="typeValue == 1">
						<view id="code" class="code-input">{{dalink.testContractAddress}}</view>
						<view class="btn-copy ads-copy" @click="copy(dalink.testContractAddress)" data-clipboard-action="copy" data-clipboard-target="#code" alt="">复制</view>
					</view>
					<view class="tips" v-if="typeValue == 1">银行卡姓名</view>
					<view class="input-code" v-if="typeValue == 1">
						<view id="code" class="code-input">{{dalink.contractAddress}}</view>
						<view class="btn-copy ads-copy" @click="copy(dalink.contractAddress)" data-clipboard-action="copy" data-clipboard-target="#code" alt="">复制</view>
					</view>
					<view class="tips" v-if="typeValue == 3">收款二维码</view>
					<view class="input-code" v-if="typeValue == 3">
						<image :src="dalink.memo | jointPic" mode="widthFix"></image>
					</view>
					<view class="tips" v-if="typeValue == 1">充值凭证</view>
					<view class="input-text" v-if="typeValue == 1">工作日48小时内有效，非工作日顺延</view>
					<view class="flexBox" v-if="typeValue == 1">
						<view @tap="avatarChoose" class="upload-item">
							<image class="image" v-if="pic" :src="pic" mode="widthFix"></image>
							<image class="btn-upload" v-else src="https://boshi.channce.com/imgs/yxstatic/icon/btn-upload.png" mode="widthFix"></image>
						</view>
					</view>
					<view class="tips">备注</view>
					<view class="input-box">
						<input class="input number" type="number" name="num" v-model="rechargeFrom.b" placeholder="输入备注">
					</view>
					<view class="btn-box">
						<button class="btn" @click="confirmRecharge()">OK</button>
					</view>
				</view>
				<view class="btn-box" v-if="!bankShow">
					<button class="btn" @click="verify()">OK</button>
				</view>
			</view>

			<view class="from-box" v-if="onLineType">
				<view class="tips">充值金额</view>
				<view class="input-text">充值前先实名认证</view>
				<view class="input-box">
					<input class="input number" type="number" name="num" v-model="rechargeFrom.money" placeholder="输入将要充值的数量">
				</view>

				<view class="pay-type-list" v-if="typeValue == 14">
					<view class="tips">银行卡列表</view>
					<radio-group @change="radioBankChange">
						<view class="type-item b-b" v-for="(item, index) in bankList" :key="index">
							<view class="con"> {{ item.bankOpen }} </view>
							<label class="radio">
								<radio :value="item.signNo" color="#fa436a" />
							</label>
						</view>
					</radio-group>
				</view>

				<view class="btn-box">
					<button class="btn" v-if="typeValue == 12 || typeValue == 14 || typeValue == 13" @click="confirmOnlineRecharge()">OK</button>
				</view>
			</view>
		</view>
		<uni-popup ref="codeBox" type="bottom">
			<view class="popBox">
				<view class="popBox-input">
					<view class="popBox-input-title">短信验证码</view>
					<input type="password" v-model="confirmFrom.smsCode" placeholder="输入短信验证码" />
				</view>
				<button class="popBox-btn" @click="confirmBank()">確定付款</button>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import Base64 from 'base-64';
	export default {
		components: {},
		data() {
			return {
				userId: uni.getStorageSync('userInfo').userId,
				kindId: null,
				pageData: {},
				rechargeFrom: {},
				pic: null,
				dalink: {},
				typelist: [
					{ id: 12, name: '支付宝', onLine: true },
					{ id: 13, name: '微信', onLine: true },
					{ id: 14, name: '银行卡', onLine: true },
					{ id: 1, name: '银行卡审核', onLine: false },
				],
				typeValue: 12,
				onLineType: true,
				bankShow: false,
				confirmFrom: {},


				bankList: [],
				signNo: ""

			};
		},
		onLoad(options) {
			this.kindId = options.id
			this.kindName = options.kindName
		},
		onShow() {
			this.getData();
		},
		methods: {
			getData() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ chainId: 7, kindId: this.kindId }, true), a_m: 'da_link' },
					success: res => { this.dalink = res.data[0] }
				});
				this.http({
					data: { a_d: this.$rsa.encrypt({ u: this.userId, i: this.kindId }, true), a_m: 'da_allMoney' },
					success: res => { if (res.code == 2000) { this.pageData = res.data[0]; } }
				});
				this.http({
					data: { a_d: this.$rsa.encrypt({ limit: 20, offset: 1, uid: uni.getStorageSync('userInfo').userId }, true), a_m: 'subscribersBank2_getBankCardNo' },
					success: res => { if (res.code == 2000) { this.bankList = res.data.rows } }
				});
			},
			// 选择充值方式
			changeType(item) {
				this.typeValue = item.id;
				this.onLineType = item.onLine
			},
			radioBankChange(event) {
				this.signNo = event.detail.value;
			},
			bankInput() {
				if (this.rechargeFrom.money < 30000) {
					this.bankShow = false
				}
			},
			verify() {
				console.log(this.rechargeFrom.money >= 30000);
				if (this.rechargeFrom.money >= 30000) {
					this.bankShow = true
				} else {
					this.bankShow = false
					this.$api.msg('银行卡审核充值金额不得小于30000元');
				}
			},
			confirmRecharge() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							b: this.rechargeFrom.b,
							i: 1,
							p: this.pic,
							m: this.rechargeFrom.money,
							s: 3,
							u: this.userId,
							c: 7
						}, true),
						a_m: 'da_recharge'
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							setTimeout(() => {
								this.rechargeFrom = {};
								this.pic = '';
								this.loadData();
								this.getData();
							}, 1000);
						}
					}
				});
			},
			confirmOnlineRecharge() {
				// 无银行卡提示
				if (this.typeValue == 11 && this.bankList.length == 0) {
					this.$api.msg('暂无可用银行卡，请先签约银行卡')
					return
				}
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							payWay: this.typeValue,
							money: this.rechargeFrom.money,
							chnlCardCd: this.signNo,
							userId: this.userId,
						}, true),
						a_m: 'rec_rechargeBalance'
					},
					success: res => {
						this.$api.msg(res.message);
						this.payCallback(res)
						// 支付宝支付处理
					}
				});
			},

			// 支付回调处理
			payCallback(res) {
				switch (this.typeValue) {
					case 14:
						// 银行卡支付处理
						this.$refs.codeBox.open('bottom');
						this.confirmFrom = res.data;
						break;
					case 13:
						// 支付宝支付处理
						if (res.data.qrData) {
							// #ifdef H5
							location.href = res.data.qrData
							// #endif
							// #ifdef APP-PLUS
							plus.runtime.openURL(res.data.qrData);
							// #endif
							setTimeout(() => {
								uni.redirectTo({
									url: `/pages/money/paySuccess`
								});
							}, 2000);
						} else {
							this.$api.msg(res.data.respMsg);
							return;
						}
						break;
					case 12:
						// 支付宝支付处理
						if (res.data.qrData) {
							// #ifdef H5
							location.href = res.data.qrData
							// #endif
							// #ifdef APP-PLUS
							plus.runtime.openURL(res.data.qrData);
							// #endif
							setTimeout(() => {
								uni.redirectTo({
									url: `/pages/money/paySuccess`
								});
							}, 2000);
						} else {
							this.$api.msg(res.data.respMsg);
							return;
						}
						break;
				}
			},
			// 银行卡确认支付
			confirmBank() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							adddata1: this.confirmFrom.adddata1,
							chnlCardCd: this.signNo,
							orderNo: this.confirmFrom.orderNo,
							userId: uni.getStorageSync('userInfo').userId,
							vercode: this.confirmFrom.smsCode,
						}, true),
						a_m: 'rec_yunfastBankPay'
					},
					success: res => {
						if (res.code == 2000) {
							this.$api.msg('充值成功');
							this.$refs.codeBox.close
						} else {
							this.$api.msg(res.message);
						}
					}
				});
			},


			encryption(item) {
				// base64加密密码
				var en = Base64.encode(item);
				// rsa加密
				var app = getApp();
				const encryptor = new this.$jsencrypt();
				const publicKey = uni.getStorageSync('token');
				encryptor.setPublicKey(publicKey);
				return encryptor.encrypt(en);
			},
			copy(value) {
				uni.setClipboardData({
					data: value,
					success: () => {
						this.$api.msg("copy success");
					}
				});
			},
			avatarChoose() {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						uni.uploadFile({
							url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
							filePath: res.tempFilePaths[0], // 图片本地路径
							name: 'file', // 固定
							success: res => {
								let data = JSON.parse(res.data).data[0].fileName
								that.pic = that.$options.filters['jointPic'](data)
							}
						});
					}
				});
			},
			avatarChoose1() {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						uni.uploadFile({
							url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
							filePath: res.tempFilePaths[0], // 图片本地路径
							name: 'file', // 固定
							success: res => {
								that.pic1 = that.jointPic(JSON.parse(res.data).data[0].fileName);
							}
						});
					}
				});
			},
			avatarChoose2() {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						uni.uploadFile({
							url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
							filePath: res.tempFilePaths[0], // 图片本地路径
							name: 'file', // 固定
							success: res => {
								that.pic2 = that.jointPic(JSON.parse(res.data).data[0].fileName);
							}
						});
					}
				});
			},


			saveImg(url) {
				var oA = document.createElement("a");
				oA.download = ''; // 设置下载的文件名，默认是'下载'
				oA.href = url;
				document.body.appendChild(oA);
				oA.click();
				oA.remove(); // 下载之后把创建的元素删除
			},

		}
	};
</script>

<style lang="scss">
	.page {
		padding-top: 40rpx;
		background-color: #ffffff;

		.page-box {
			width: 90vw;
			margin: 0 auto;
			border-radius: 5px;
			padding: 20rpx 40rpx;
			background: $uni-color-primary;
			color: #ffffff;

			:nth-child(1) {
				font-weight: 600;
				font-size: 18px;
			}

			:nth-child(2) {
				padding: 10px 20px;
			}
		}


		.from {
			width: 85vw;
			margin: 0 auto;

			.type {
				display: flex;
				align-items: center;
				width: 90vw;
				margin: 0 auto;
				// background-color: #FFFFFF;

				view {
					// width: 50%;
					font-size: 15px;
					text-decoration: none;
					height: 40px;
					display: flex;
					justify-content: center;
					align-items: center;
					margin-right: 40rpx;
				}

				.is_li {
					cursor: pointer;
					color: $primary-btn-color;
					position: relative;
				}

				.is_li::after {
					content: '';
					background: $primary-btn-color;
					height: 4rpx;
					width: 50%;
					position: absolute;
					bottom: 0px;
				}
			}

			.from-box {
				margin: 20rpx auto 0;
				padding-bottom: 20px;
			}

			.tips {
				padding: 19px 0 14px;
				font-size: 14px;
			}

			.input-box {
				padding: 12px;
				background: #ffffff;
				border: 1px solid #CDCDCD;
				border-radius: 5px;

				.input {
					width: 100%;
					box-sizing: border-box;
					outline: none;
				}
			}

			.input-text {
				font-size: 12px;
				color: #a5a5a5;
				margin-bottom: 5px;
			}

			.flexBox {
				display: flex;
				width: 100%;

				.upload-item {
					width: 30%;
					margin-right: 20px;

					.btn-upload {
						width: 100%;
					}

					.image {
						width: 100%;
					}
				}


			}


			.input-code {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 12px;
				background: #ffffff;
				border: 1px solid #CDCDCD;
				border-radius: 5px;

				.code-input {
					flex: 1;
					padding-right: 8px;
				}

				.btn-copy {
					width: 34px;
					color: #4e6eb7;
					border: 1px solid #4e6eb7;
					font-size: 12px;
					border-radius: 5px;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}


			.btn-box {
				margin-top: 20px;

				.btn {
					width: 100%;
					height: 40px;
					font-size: 14px;
					border-radius: 6px;
					line-height: 40px;
					color: #ffffff;
					background: $uni-color-primary;
					text-align: center;
				}
			}
		}




		.pay-type-list {
			margin-top: 20upx;
			background-color: #fff;

			.type-item {
				height: 120upx;
				padding: 20upx 0;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 30upx;
				position: relative;

				.tit {
					font-size: $font-lg;
					color: $font-color-dark;
					margin-bottom: 4upx;
				}

				.con-box {
					border: 1px solid #fe8e2e;
					width: fit-content;
					padding: 4rpx 10rpx;
					border-radius: 10rpx;
				}
			}
		}
	}
</style>