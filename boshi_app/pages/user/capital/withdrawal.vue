<template>
	<view class="page">
		<view class="page-box">
			<view class="page-box-tit">
				<view class="text"> {{pageData.name}}</view>
				<view class="text"> {{pageData.money}} </view>
			</view>
			<view class="page-box-btn" @click="navTo('/pages/user/capital/withdrawalRecord')">提现记录</view>
		</view>
		<view class="from">
			<view class="from-box">
				<view class="tips">银行卡号</view>
				<view class="input-box">
					<view class="input-left">
						<input class="input address" type="text" name="address" v-model="withdrawFrom.a" placeholder="请输入银行卡号">
					</view>
				</view>
				<view class="tips">银行卡姓名</view>
				<view class="input-box">
					<input class="input number" type="text" name="num" v-model="withdrawFrom.b" placeholder="请输入银行卡姓名">
				</view>

				<view class="tips">银行卡开户行</view>
				<view class="input-box">
					<input class="input number" type="text" name="num" v-model="withdrawFrom.c" placeholder="请入银行卡开户行">
				</view>

				<view class="tips">手机号</view>
				<view class="input-box">
					<input class="input number" type="text" name="num" v-model="withdrawFrom.e" placeholder="请入银行卡手机号">
				</view>

				<view class="tips" v-if="this.current == 1">提现数量</view>
				<view class="tips" v-if="this.current == 5">兑换数量</view>
				<view class="input-box" v-if="this.current == 1">
					<input class="input number" type="text" name="num" v-model="withdrawFrom.money" placeholder="提现数量为100的倍数" @input="inputChange">
				</view>
				<view class="input-box" v-if="this.current == 5">
					<input class="input number" type="text" name="num" v-model="withdrawFrom.money" placeholder="兑换数量为100的倍数" @input="inputChange">
				</view>


				<view class="box-text">实际到账{{dzNum}}</view>

				<view class="tips">支付密码</view>
				<view class="input-box">
					<input class="input number" type="password" name="num" v-model="withdrawFrom.password" placeholder="请输入支付密码">
				</view>

				<!-- <view class="tips">备注</view>
				<view class="input-box">
					<input class="input number" type="text" name="num" v-model="withdrawFrom.remarks" placeholder="输入备注">
				</view> -->
				<!-- <view class="input-checkbox">
					<checkbox value="true" style="transform:scale(0.7)" :checked="isAgreement" @change='change' />
					<view class=""> 勾选确认 已完成自由职业注册认证 </view>
				</view>
				<view class="box-text">
					若未完成, <text class="go" @click="go()">注册认证</text> 点击添加同样银行卡
				</view> -->

				<view class="btn-box">
					<button id="submit" type="button" class="btn" @click="confirmWthdraw()">OK</button>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import Base64 from 'base-64';
	export default {
		data() {
			return {
				withdrawFrom: {},
				pageData: {},
				userId: uni.getStorageSync('userInfo').userId,
				// typeList: [{
				// 	id: 5,
				// 	name: "余额"
				// }, {
				// 	id: 4,
				// 	name: "代运营收益"
				// }],
				current: 1,
				dzNum: 0,
				isAgreement: false,
			};
		},
		onShow() {
			this.getData();
		},
		onLoad(option) {
			if (option.id) {
				this.current = option.id
			}
		},
		methods: {
			navTo(url) {
				uni.navigateTo({ url });
			},
			getData() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ i: this.current, u: this.userId }, true), a_m: 'da_allMoney' },
					success: res => { if (res.code == 2000) { this.pageData = res.data[0]; } }
				});
				this.http({
					data: { a_d: this.$rsa.encrypt({ u: this.userId }, true), a_m: 'da_last' },
					success: res => {
						if (res.data != '') {
							let text = res.data.split(" ")
							this.withdrawFrom.a = text[0]
							this.withdrawFrom.b = text[1]
							this.withdrawFrom.c = text[2]
							this.withdrawFrom.e = text[3]
						}
					}
				});
			},
			inputChange() {
				this.http({
					data: { a_d: this.$rsa.encrypt({ i: this.current, m: this.withdrawFrom.money }, true), a_m: 'da_dz' },
					success: res => { if (res.code == 2000) { this.dzNum = res.data.a; } }
				});
			},
			change(e) {
				console.log(e);
			},
			// 确认提现
			confirmWthdraw() {
				console.log(this.withdrawFrom);
				if (!this.withdrawFrom.a || !this.withdrawFrom.b || !this.withdrawFrom.c || !this.withdrawFrom.e) {
					this.$api.msg('确认银行卡信息输入正确');
					return
				}
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							b: this.withdrawFrom.remarks,
							category: 4,
							chain: `${this.withdrawFrom.a} ${this.withdrawFrom.b} ${this.withdrawFrom.c} ${this.withdrawFrom.e}`,
							chainId: 7,
							cid: 56,
							er: this.encryption(this.withdrawFrom.password),
							i: this.current,
							m: this.withdrawFrom.money,
							s: 4,
							u: this.userId
						}, true),
						a_m: 'da_withdraw'
					},
					success: res => {
						this.$api.msg(res.message);
						if (res.code == 2000) {
							setTimeout(() => {
								this.withdrawFrom = {};
								this.pic = '';
								this.loadData();
								this.getData();
							}, 1000);
						}
					}
				});
			},
			encryption(item) {
				// base64加密密码
				var en = Base64.encode(item);
				// rsa加密
				var app = getApp();
				const encryptor = new this.$jsencrypt();
				const publicKey = uni.getStorageSync('token');
				encryptor.setPublicKey(publicKey);
				return encryptor.encrypt(en);
			},
			go() {
				plus.runtime.openURL('https://www.zlbzb.cn/iv.html?i=Y1702966680739524610');
			}

		}
	};
</script>

<style lang="scss">
	.page {
		padding-top: 40rpx;
		padding-bottom: 40rpx;
		background-color: #ffffff;

		.typeSelect {
			display: flex;
			align-items: center;
			width: 90vw;
			margin: 0 auto 10px;

			view {
				font-size: 16px;
				font-weight: bold;
				text-decoration: none;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 40rpx;
				cursor: pointer;
				position: relative;
			}

			.active {
				color: $primary-btn-color;
			}

			.active::after {
				content: '';
				background: $primary-btn-color;
				height: 4rpx;
				width: 50%;
				position: absolute;
				bottom: 0px;
			}
		}


		.page-box {
			width: 90vw;
			margin: 0 auto;
			border-radius: 5px;
			padding: 40rpx;
			background: $uni-color-primary;
			color: #000000;
			display: flex;
			justify-content: space-between;

			.page-box-tit {
				:nth-child(2) {
					color: #ffffff;
					padding: 10px 20px;
				}
			}

			.page-box-btn {
				color: #d8e5fe;
				font-size: 12px;
			}
		}


		.from {
			width: 85vw;
			margin: 0 auto;

			.from-box {
				margin: 20rpx auto 0;
			}

			.tips {
				padding: 15px 0 5px;
				font-size: 14px;
			}

			.input-box {
				padding: 16rpx 12px;
				background: #ffffff;
				border: 1px solid #CDCDCD;
				border-radius: 5px;

				.input {
					width: 100%;
					color: #000000;
					box-sizing: border-box;
					outline: none;
				}
			}

			.box-text {
				font-size: 28rpx;
				padding-top: 10rpx;
				text-align: end;


			}

			.btn-box {
				margin-top: 20px;

				.btn {
					width: 100%;
					height: 40px;
					font-size: 14px;
					border-radius: 6px;
					line-height: 40px;
					color: #ffffff;
					background: $uni-color-primary;
					text-align: center;
				}
			}

			.input-checkbox {
				font-size: 26rpx;
				color: #000000;
				white-space: break-spaces;
				width: 100%;
				font-weight: 600;
				display: flex;
				align-items: center;
				margin-top: 20rpx;

				::v-deep .uni-checkbox-input {
					border-radius: 50%;
				}

				text {
					color: $primary-btn-color;
				}
			}
		}

		.go {
			color: $uni-color-primary;
		}

	}
</style>