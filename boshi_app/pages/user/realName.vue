<template>
	<view class="page">
		<view class="from">
			<view class="from-title">填写实名信息</view>
			<view class="from-item">
				<view>真实姓名</view>
				<input type="text" placeholder="请输入真实姓名" v-model="userName" />
			</view>
			<view class="from-item">
				<view>身份证号码</view>
				<input type="text" placeholder="请输入身份证号码" v-model="idCard" />
			</view>
		</view>
		<view class="upload">
			<view class="upload-title">身份证照片</view>
			<view class="upload-item" @tap="avatarChooses()">
				<image class="uploadItem-box" v-if="idCardPic" :src="idCardPic | jointPic" mode="widthFix"></image>
				<image class="uploadItem-box" v-else src="https://boshi.channce.com/imgs/yxstatic/icon/add1.png" mode="widthFix"></image>
				<view class="">点击上传身份证头像面</view>
			</view>
		</view>
		<view class="uifrom-save" v-if="isAudit == -1 "><button class="save" @tap="savaInfo()">确 认 申 请</button></view>
		<view class="uifrom-save" v-if="isAudit == 2"><button class="save" @tap="savaInfo()">已拒绝 重新申请</button></view>
		<view class="uifrom-save-no" v-if="isAudit == 0"><button class="save">等 待 审 核</button></view>
		<!-- <view class="uifrom-save-no" v-if="isAudit == 1"><button class="save">已 通 过</button></view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				from: {},
				idCardPic: null,
				userName: '',
				idCard: '',
				isAudit: null,
				a: "",
				b: "",
				c: "",
			};
		},
		onShow() {
			this.getData();
		},
		methods: {
			getData() {
				this.http({
					url: '/api/subscribersCertification/certification',
					method: 'post',
					data: {
						userId: uni.getStorageSync('userInfo').userId,
					},
					success: res => {
						console.log(res.data);
						this.isAudit = res.data.isAudit
						if (this.isAudit == -1) { return }
						this.idCard = res.data.idCard;
						this.userName = res.data.userName;
						this.idCardPic = res.data.idCardPic;
					}
				});
			},

			savaInfo() {
				console.log(this.idCard, this.userName, this.idCardPic);
				this.http({
					url: '/api/subscribersCertification/apply',
					method: 'post',
					data: {
						idCard: this.idCard,
						userName: this.userName,
						idCardPic: this.idCardPic,
						idCardType: 1,
						userId: uni.getStorageSync('userInfo').userId,
					},
					success: res => {
						this.$api.msg(res.message);
					}
				});
			},



			avatarChooses() {
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						uni.uploadFile({
							url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
							filePath: res.tempFilePaths[0], // 图片本地路径
							name: 'file', // 固定
							success: res => {
								that.idCardPic = JSON.parse(res.data).data[0].fileName;
							}
						});
					}
				});
			},
		}
	};
</script>

<style lang="scss">
	.page {
		background-color: #f4f4f4;
		min-height: 100vh;
		padding-top: 40rpx;
	}

	.ui-all {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 40upx auto;

	}

	.ui-all-title {
		padding-top: 20rpx;
		padding-bottom: 20rpx;
		font-weight: 600;
	}

	.ui1-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;
	}

	.ui-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;
	}

	.pic-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto;
		// text-align: center;
	}

	.uifrom {
		display: flex;
		justify-content: space-between;
		padding: 20upx 0;
		font-size: 28upx;

		input {
			width: 70%;
			font-size: 28upx;
		}
	}

	.imgAvatar {
		width: 100%;

		.iavatar {
			width: 100%;
		}

		.iavatars {
			width: 200rpx;
		}
	}

	.from {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;

		.from-title {
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			font-weight: 600;
		}

		.from-item {
			display: flex;
			justify-content: space-between;
			padding: 20upx 0;
			font-size: 28upx;

			input {
				width: 70%;
				font-size: 28upx;
			}
		}
	}

	.upload {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx 40rpx;
		border-radius: 20upx;
		margin: 30upx auto 0;


		// text-align: center;
		.upload-title {
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			font-weight: 600;
		}


		.upload-item {
			width: 100%;
			text-align: center;
			margin-top: 40rpx;

			.uploadItem-box {
				width: 50vw;
			}

			view {
				font-size: 14px;
				padding-top: 10rpx;
			}
		}

		.upload-msg {
			font-size: 12px;
			color: #787878;
			margin: 10rpx auto;
			width: 95%;
		}

		.top {
			margin-top: 40rpx;
		}

	}

	.uifrom-save {
		width: 100%;
		position: fixed;
		bottom: 0;
		padding: 30rpx 0;

		.save {
			background: #5096ff;
			color: #ffffff;
			margin-top: 40rpx auto;
			font-size: 28rpx;
			width: 95vw;
			border-radius: 50upx;
		}
	}

	.uifrom-save-no {
		width: 100%;
		position: fixed;
		bottom: 0;
		padding: 30rpx 0;

		.save {
			background: #c0d1ff;
			color: #ffffff;
			margin-top: 40rpx auto;
			font-size: 28rpx;
			width: 95vw;
			border-radius: 50upx;
		}
	}
</style>