<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="nav-bar">
			<view class="nav-title">房屋装修</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<uni-search-bar placeholder="搜索装修商品" bgColor="#EEEEEE" radius="100" @confirm="search" v-model="searchValue" cancelButton="none"></uni-search-bar>
		</view>

		<!-- 分类导航 -->
		<view class="category-nav">
			<scroll-view scroll-x class="category-scroll">
				<view class="category-item" 
					  v-for="(item, index) in categories" 
					  :key="index"
					  :class="{active: currentCategory === index}"
					  @click="changeCategory(index)">
					{{item.name}}
				</view>
			</scroll-view>
		</view>

		<!-- 商品列表 -->
		<view class="goods-list">
			<view class="goods-item" v-for="(item, index) in goodsList" :key="index" @click="goToDetail(item)">
				<image :src="item.image" mode="aspectFill" class="goods-image"></image>
				<view class="goods-info">
					<view class="goods-name">{{item.name}}</view>
					<view class="goods-desc">{{item.description}}</view>
					<view class="goods-price">
						<text class="price-symbol">￥</text>
						<text class="price-value">{{item.price}}</text>
						<text class="original-price">￥{{item.originalPrice}}</text>
					</view>
					<view class="goods-tags">
						<text class="tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<empty v-if="goodsList.length === 0"></empty>
	</view>
</template>

<script>
	import empty from "@/components/empty.vue";
	
	export default {
		components: { empty },
		data() {
			return {
				searchValue: '',
				currentCategory: 0,
				categories: [
					{ name: '全部', id: 0 },
					{ name: '客厅', id: 1 },
					{ name: '卧室', id: 2 },
					{ name: '厨房', id: 3 },
					{ name: '卫浴', id: 4 },
					{ name: '阳台', id: 5 }
				],
				goodsList: [
					{
						id: 1,
						name: '现代简约沙发',
						description: '舒适透气，简约设计',
						price: 2999,
						originalPrice: 3999,
						image: '/static/mall/<EMAIL>',
						tags: ['热销', '包邮']
					},
					{
						id: 2,
						name: '实木餐桌椅组合',
						description: '环保实木，经久耐用',
						price: 1899,
						originalPrice: 2599,
						image: '/static/mall/<EMAIL>',
						tags: ['新品', '环保']
					},
					{
						id: 3,
						name: '智能马桶盖',
						description: '智能清洁，健康卫生',
						price: 899,
						originalPrice: 1299,
						image: '/static/mall/<EMAIL>',
						tags: ['智能', '特价']
					}
				]
			};
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			search() {
				console.log('搜索:', this.searchValue);
				this.loadData();
			},
			changeCategory(index) {
				this.currentCategory = index;
				this.loadData();
			},
			loadData() {
				// 模拟数据加载
				console.log('加载数据，分类:', this.currentCategory, '搜索:', this.searchValue);
			},
			goToDetail(item) {
				uni.navigateTo({
					url: `/pages/product/product?goodsId=${item.id}`
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		background: #f5f5f5;
		min-height: 100vh;
	}

	.nav-bar {
		background: #fff;
		height: 88upx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px solid #eee;

		.nav-title {
			font-size: 36upx;
			font-weight: 600;
			color: $font-color-dark;
		}
	}

	.search-section {
		background: #fff;
		padding: 20upx;
	}

	.category-nav {
		background: #fff;
		padding: 20upx 0;
		border-bottom: 1px solid #eee;

		.category-scroll {
			white-space: nowrap;
			padding: 0 20upx;

			.category-item {
				display: inline-block;
				padding: 16upx 32upx;
				margin-right: 20upx;
				background: #f8f8f8;
				border-radius: 40upx;
				font-size: 28upx;
				color: $font-color-base;

				&.active {
					background: $uni-color-primary;
					color: #fff;
				}
			}
		}
	}

	.goods-list {
		padding: 20upx;

		.goods-item {
			background: #fff;
			border-radius: 16upx;
			margin-bottom: 20upx;
			padding: 20upx;
			display: flex;

			.goods-image {
				width: 200upx;
				height: 200upx;
				border-radius: 12upx;
				margin-right: 20upx;
			}

			.goods-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.goods-name {
					font-size: 32upx;
					font-weight: 600;
					color: $font-color-dark;
					margin-bottom: 10upx;
				}

				.goods-desc {
					font-size: 26upx;
					color: $font-color-light;
					margin-bottom: 20upx;
				}

				.goods-price {
					display: flex;
					align-items: baseline;
					margin-bottom: 20upx;

					.price-symbol {
						font-size: 24upx;
						color: $uni-color-primary;
					}

					.price-value {
						font-size: 36upx;
						font-weight: 600;
						color: $uni-color-primary;
						margin-right: 20upx;
					}

					.original-price {
						font-size: 24upx;
						color: $font-color-light;
						text-decoration: line-through;
					}
				}

				.goods-tags {
					display: flex;

					.tag {
						font-size: 20upx;
						color: $uni-color-primary;
						background: rgba($uni-color-primary, 0.1);
						padding: 4upx 12upx;
						border-radius: 20upx;
						margin-right: 10upx;
					}
				}
			}
		}
	}
</style>
