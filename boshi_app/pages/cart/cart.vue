<template>
	<view class="container">
		<!-- 空白页 -->
		<view v-if="cartList.length == 0" class="empty">
			<image src="https://boshi.channce.com/imgs/static/icon/noMore.png" mode="aspectFit"></image>
			<view v-if="isLogin" class="empty-tips"> 暂无数据 </view>
			<view v-else class="empty-tips">
				暂无数据 <view class="navigator" @click="navToLogin">去登陆></view>
			</view>
		</view>

		<view v-else>
			<!-- 列表 -->
			<view class="cart-list">
				<block v-for="(item, index) in cartList" :key="index">
					<view class="cart-item" :class="{'b-b': index!==cartList.length-1}" @click="check('item', index)">
						<view class="yticon icon-xuanzhong2 checkbox" :class="{checked: item.checked}"></view>
						<view class="image-wrapper">
							<image :src="item.showPic | jointPic" mode="aspectFill"></image>
						</view>
						<view class="item-right" @click.stop>
							<text class="clamp title">{{item.name}}</text>
							<view class="price">
								<view>￥{{item.money}}</view>
							</view>
							<view class="attr">{{item.sizes}} {{item.standard}}</view>
						</view>
						<text class="del-btn yticon icon-fork" @click="deleteCartItem(item.uuid)"></text>
					</view>
				</block>
			</view>
		</view>
		<!-- 底部菜单栏 -->
		<view class="action-section">
			<view class="checkbox">
				合计:￥{{total}}
			</view>
			<button type="primary" class="no-border confirm-btn" @click="createOrder">去结算</button>
		</view>
	</view>
</template>

<script>
	import { mapMutations, mapState } from 'vuex';
	export default {
		data() {
			return {
				total: 0, //总价格
				deductionTotal: 0,
				allChecked: false, //全选状态  true|false
				empty: false, //空白页现实  true|false
				cartList: [],
				token: uni.getStorageSync('token')
			};
		},
		onShow() {
			if (this.isLogin) {
				this.getData();
			}
		},
		computed: {
			...mapState(['isLogin', 'userInfo'])
		},
		watch: {
			cartList(e) {
				let empty = e.length === 0 ? true : false;
				if (this.empty !== empty) {
					this.empty = empty;
				}
			}
		},
		methods: {
			...mapMutations(['getDetail', 'delDetail']),
			getData() {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							userId: uni.getStorageSync('userInfo').userId
						}, true),
						a_m: "cart_cartList",
					},
					success: res => {
						this.cartList = res.data.map(item => {
							item.checked = true;
							return item;
						});
						this.calcTotal(); //计算总价
					}
				})
			},
			navToLogin() {
				// #ifndef MP-WEIXIN
				uni.navigateTo({ url: '/pages_account/login' })
				// #endif
				// #ifdef MP-WEIXIN 
				uni.navigateTo({ url: '/pages_account/wx_login' })
				// #endif
			},
			navTo(item) { uni.navigateTo({ url: item }); },
			//选中状态处理
			check(type, index) {
				if (type === 'item') {
					this.cartList[index].checked = !this.cartList[index].checked;
				} else {
					const checked = !this.allChecked
					const list = this.cartList;
					list.forEach(item => {
						item.checked = checked;
					})
					this.allChecked = checked;
				}
				this.calcTotal(type);
			},
			//数量
			numberChange(data) {
				let flag = 0
				if (this.cartList[data.index].count <= data.number) {
					flag = 1
				} else if (this.cartList[data.index].count >= data.number) {
					flag = 2
				}
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							count: 1,
							flag: flag,
							id: this.cartList[data.index].id,
							standard: this.cartList[data.index].standard,
							userId: uni.getStorageSync('userInfo').userId,
							uuid: this.cartList[data.index].uuid
						}, true),
						a_m: "cart_addAndSubtract",
					},
					success: res => {
						if (res.code === 2000) {
							this.cartList[data.index].count = data.number;
						}
					},
					fail: err => {
						this.$api.msg(res.message);
					}
				})
				this.calcTotal();
			},
			//删除
			deleteCartItem(item) {
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							userId: uni.getStorageSync('userInfo').userId,
							uuid: item
						}, true),
						a_m: "cart_cartDel",
					},
					success: res => {
						this.getData()
						this.$api.msg(res.message);
					},
					fail: err => {
						this.$api.msg(res.message);
					}
				})
			},
			//清空
			clearCart() {
				var uuid = ""
				for (let i = 0; i < this.cartList.length; i++) {
					uuid += this.cartList[i].uuid + ","
				}
				this.http({
					data: {
						a_d: this.$rsa.encrypt({
							userId: uni.getStorageSync('userInfo').userId,
							uuid: item
						}, true),
						a_m: "cart_cartDel",
					},
					success: res => {
						this.getData()
						this.$api.msg(res.message);
					},
					fail: err => {
						this.$api.msg(res.message);
					}
				})
			},
			//计算总价
			calcTotal() {
				let list = this.cartList;
				if (list.length === 0) {
					this.empty = true;
					return;
				}
				let total = 0;
				let deductionTotal = 0;
				let checked = true;
				list.forEach(item => {
					if (item.checked === true) {
						total += item.money * item.count;
						deductionTotal += item.deductionMoney * item.count;
					} else if (checked === true) {
						checked = false;
					}
				})
				this.allChecked = checked;
				this.total = Number(total.toFixed(2));
				this.deductionTotal = Number(deductionTotal.toFixed(2));
			},

			//创建订单
			createOrder() {
				this.delDetail()
				for (let i = 0; i < this.cartList.length; i++) {
					// console.log(this.cartList[i]);
					if (this.cartList[i].checked == true) {
						this.getDetail(this.cartList[i])
					}
				}
				// return
				uni.navigateTo({
					url: `/pages/order/createOrder?type=cart`
				})
			}
		}
	}
</script>

<style lang='scss'>
	page {
		background: #f5f5f5;
	}

	.container {
		padding-bottom: 134upx;

		/* 空白页 */
		.empty {
			position: fixed;
			left: 0;
			top: -160upx;
			width: 100%;
			height: 100vh;
			padding-bottom: 100upx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;

			image {
				width: 340upx;
				margin-bottom: 30upx;
			}

			.empty-tips {
				display: flex;
				font-size: calc(#{$font-sm} + 2upx);
				color: $font-color-disabled;

				.navigator {
					color: $uni-color-primary;
					margin-left: 16upx;
				}
			}
		}
	}

	/* 购物车列表项 */
	.cart-list {
		width: 95vw;
		margin: 20upx auto;
		background-color: #fff;
		border-radius: 10upx;
	}

	.cart-item {
		display: flex;
		align-items: center;
		padding: 30upx 20upx;

		.image-wrapper {
			width: 170upx;
			height: 170upx;

			image {
				border-radius: 8upx;
			}
		}

		.checkbox {
			z-index: 8;
			font-size: 44upx;
			line-height: 1;
			padding: 4upx;
			color: $font-color-disabled;
			background: #fff;
			border-radius: 50px;
			margin-right: 10rpx;
		}

		.item-right {
			display: flex;
			flex-direction: column;
			flex: 1;
			overflow: hidden;
			position: relative;
			padding-left: 30upx;
			height: 200upx;

			.title,
			.price {
				font-size: calc(#{$font-base} + 2upx);
				color: $font-color-dark;
				height: 40upx;
				line-height: 40upx;
			}

			.attr {
				font-size: calc(#{$font-sm} + 2upx);
				color: $font-color-light;
				height: 50upx;
				line-height: 50upx;
			}

			.price {
				height: 50upx;
				line-height: 50upx;
				display: flex;

				text {
					padding-right: 20rpx;
				}
			}
		}

		.del-btn {
			padding: 4upx 10upx;
			font-size: 34upx;
			height: 50upx;
			color: $font-color-light;
		}
	}

	/* 底部栏 */
	.action-section {
		position: fixed;
		bottom: 0upx;
		z-index: 95;
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100vw;
		height: 90upx;
		background: rgba(255, 255, 255, .9);
		box-shadow: 0px -2px 10px 1px rgba(88, 88, 88, 0.6);

		.checkbox {
			height: 48upx;
			position: relative;
			padding: 0 50upx;
			color: $uni-color-primary;

			image {
				width: 52upx;
				height: 100%;
				position: relative;
				z-index: 5;
			}
		}

		.confirm-btn {
			padding: 0 50upx;
			margin: 0;
			border-radius: 50px 0 0 50px;
			height: 100%;
			line-height: 90upx;
			font-size: calc(#{$font-base} + 2upx);
			background: $uni-color-primary;
		}
	}

	/* 复选框选中状态 */
	.action-section .checkbox.checked,
	.cart-item .checkbox.checked {
		color: $uni-color-primary;
	}
</style>
