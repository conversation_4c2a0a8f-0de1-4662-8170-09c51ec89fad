<template>
	<view class="page">
		<uni-swipe-action>
			<view class="list" @click="bindClick" v-for="item in list" :key="item.index">
				<uni-swipe-action-item class="list-item">
					<template v-slot:left>
						<!-- <view class="slot-button"><text class="slot-button-text" @click="bindClick({ position: 'left', content: { text: '置顶' } })">置顶</text></view> -->
					</template>
					<view class="content-box" @click="contentClick">
						<image class="stroeListItem-pic" :src="item.pic | jointPic" mode="aspectFill"></image>
						<view class="stroeListItem-right">
							<view class="top">{{ item.name }}</view>
							<view class="bottom">
								<view class="money">￥{{ item.price }}</view>
							</view>
							<view class="address">状态：{{ item.status == 1 ? '已下架' : '已上架' }}</view>
						</view>
					</view>
					<template v-slot:right>
						<view class="slot-button danger" @click="bindClick('del', item)"><text class="slot-button-text">删除</text></view>
						<view class="slot-button primary" v-if="item.status == 1" @click="bindClick('upGoods', item)"><text class="slot-button-text">上架</text></view>
						<view class="slot-button warning" v-if="item.status == 0" @click="bindClick('downGoods', item)"><text class="slot-button-text">下架</text></view>
						<view class="slot-button success" @click="bindClick('edit', item)"><text class="slot-button-text">编辑商品</text></view>
					</template>
				</uni-swipe-action-item>
			</view>
		</uni-swipe-action>
		<view class="bank-btn" @click="goAddBank()">添加商品</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page: 1,
				list: [],
				options: [ {
					text: '取消',
					style: {
						backgroundColor: '#007aff'
					}
				}, {
					text: '确认',
					style: {
						backgroundColor: '#dd524d'
					}
				} ]
			};
		},
		onLoad() {},
		onShow() {
			this.getData();
		},
		methods: {
			goAddBank() {
				uni.navigateTo( {
					url: `/pages/store/goodsDetail?type=add`
				} );
			},
			bindClick( e, item ) {
				switch ( e ) {
					case 'del':
						this.goodsDel( item );
						break;
					case 'upGoods':
						this.upDownGoods( item, 0 );
						break;
					case 'downGoods':
						this.upDownGoods( item, 1 );
						break;
					case 'edit':
						uni.navigateTo( {
							url: `/pages/store/goodsDetail?type=edit&goodsId=${item.id}`
						} );
						break;
				}
			},
			getData() {
				this.http( {
					data: {
						a_d: this.$rsa.encrypt( {
							userId: uni.getStorageSync( 'userInfo' ).userId,
							limit: 100,
							offset: this.page
						}, true ),
						a_m: 'merchant_merchantCommodityList'
					},
					success: res => { this.list = res.data.rows; }
				} );
			},

			scrollLower() {
				if ( this.list.length >= this.allTotal ) {
					this.status = 'noMore';
					this.$api.msg( '当前已加载完毕' );
					return;
				} else {
					this.status = 'loading';
					this.page++;
					this.getData();
				}
			},
			goodsDel( item ) {
				let that = this;
				uni.showModal( {
					title: '提示',
					content: '确定删除此商品',
					success: function( res ) {
						if ( res.confirm ) {
							that.http( {
								data: {
									a_d: that.$rsa.encrypt( { userId: uni.getStorageSync( 'userInfo' ).userId, id: item.id }, true ),
									a_m: 'merchant_merchantCommodityDel'
								},
								success: res => {
									if ( res.code == 2000 ) {
										that.$api.msg( '删除成功' );
										that.getData();
									}
								}
							} );
						} else if ( res.cancel ) {
							console.log( '用户点击取消' );
						}
					}
				} );
			},
			upDownGoods( item, status ) {
				let msg,
					that = this;
				if ( status == 0 ) {
					msg = '确定上架该商品';
				} else {
					msg = '确定将该商品下架';
				}
				uni.showModal( {
					title: '提示',
					content: msg,
					success: function( res ) {
						if ( res.confirm ) {
							that.http( {
								data: {
									a_d: that.$rsa.encrypt( { userId: uni.getStorageSync( 'userInfo' ).userId, id: item.id, status: status }, true ),
									a_m: 'merchant_merchantCommodityUp'
								},
								success: res => {
									if ( res.code == 2000 ) {
										that.$api.msg( '操作成功' );
										that.getData();
									}
								}
							} );
						} else if ( res.cancel ) {
							console.log( '用户点击取消' );
						}
					}
				} );
			}
		}
	};
</script>

<style lang="scss">
	.page {
		display: flex;
		flex-direction: column;
		padding-bottom: 150rpx;
		background-color: #ffffff;

		.list {

			.list-item {
				margin-top: 20rpx;

				.content-box {
					width: 100%;
					background-color: #ffffff;
					color: #000000;
					font-size: 14px;
					display: flex;
					justify-content: space-between;
					padding: 30rpx;

					.stroeListItem-pic {
						width: 200rpx;
						height: 200rpx;
						border-radius: 10rpx;
					}

					.stroeListItem-right {
						width: calc(100% - 240rpx);

						.top {
							font-size: 30rpx;
							font-weight: 600;
							display: -webkit-box;
							overflow: hidden;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}

						.bottom {
							display: flex;
							width: 100%;
							align-items: center;
							padding: 20rpx 0;

							.money {
								color: rgba(236, 65, 19, 1);
								font-size: 28rpx;
							}

							.btn {
								color: rgba(171, 168, 168, 0.88);
								font-size: 24rpx;
								text-decoration: line-through;
								margin-left: 2px;
							}
						}

						.address {
							font-size: 30rpx;
							font-weight: 600;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							color: #ababab;
						}

						.look {
							background-color: #ffebd8;
							color: #e08e40;
							display: inline-block;
							margin-top: 40rpx;
							padding: 4rpx 10rpx;
						}
					}
				}

				.slot-button {
					/* #ifndef APP-NVUE */
					display: flex;
					height: 100%;
					/* #endif */
					flex: 1;
					flex-direction: row;
					justify-content: center;
					align-items: center;
					padding: 0 20px;

					.slot-button-text {
						color: #ffffff;
						font-size: 14px;
					}
				}

				.warning {
					background-color: #e6a23c;
				}

				.primary {
					background-color: #409eff;
				}

				.success {
					background-color: #67c23a;
				}

				.danger {
					background-color: #f56c6c;
				}
			}
		}

		.bank-btn {
			position: fixed;
			left: 15px;
			right: 15px;
			bottom: 8px;
			z-index: 95;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 358px;
			height: 41px;
			font-size: 16px;
			color: #fff;
			background-color: $base-color;
			border-radius: 5px;
			box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);
		}
	}

	.content-text {
		font-size: 15px;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		padding: 10px 0;
		background-color: #fff;
	}

	.button {
		border-color: #e5e5e5;
		border-style: solid;
		border-width: 1px;
		padding: 4px 8px;
		border-radius: 4px;
	}

	.button-text {
		font-size: 15px;
	}
</style>