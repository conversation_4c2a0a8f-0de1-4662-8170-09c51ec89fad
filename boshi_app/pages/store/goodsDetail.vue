<template>
	<view class="container">
		<view class="ui-all">
			<view class="ui-all-title">填写商品信息</view>
			<view class="uifrom">
				<view>商品名称</view>
				<input type="text" placeholder="商品名称必填" v-model="from.name" />
			</view>
			<!-- <view class="uifrom">
				<view>门店分类</view>
				<uni-section class="section" title="本地数据" type="line">
					<uni-data-select v-model="from.storeClassifyId" :localdata="range" @change="change"></uni-data-select>
				</uni-section>
			</view> -->
			<view class="uifrom">
				<view>规格</view>
				<input type="text" placeholder="请输入商品规格" v-model="from.standard" />
			</view>
			<view class="uifrom">
				<view>尺码</view>
				<input type="text" placeholder="请输入商品尺码" v-model="from.sizes" />
			</view>
			<view class="uifrom">
				<view>型号</view>
				<input type="text" placeholder="请输入商品型号" v-model="from.model" />
			</view>
			<view class="uifrom">
				<view>库存</view>
				<input type="text" placeholder="请输入商品库存" v-model="from.stock" />
			</view>
		</view>
		<view class="ui1-box">
			<view class="uifrom">
				<view>原价</view>
				<input type="text" placeholder="请输入商品原价" v-model="from.original" />
			</view>
			<view class="uifrom">
				<view>价格</view>
				<input type="text" placeholder="请输入商品价格" v-model="from.price" />
			</view>
		</view>
		<view class="pic-box">
			<view class="uifrom">商品展示图</view>
			<view class="imgAvatars" @tap="imgUpload('pic')">
				<image class="imgAvatars-iavatar" v-if="from.pic" :src="from.pic | jointPic" mode="widthFix"></image>
				<image class="imgAvatars-iavatars" v-else src="https://boshi.channce.com/imgs/yxstatic/icon/add.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="pic-box">
			<view class="uifrom">支付宝收款码</view>
			<uni-file-picker limit="9" :value="from.slideshow" title="维修单据" @select="select" />

		</view>
		<view class="uifrom-save">
			<button class="save" @tap="savaInfo()">{{ type == 'add' ? '确 认 添 加' : '确 认 修 改' }}</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				from: {
					details: null, // 详情
					explains: null, // 说明
					isStock: false, // 是否去库存商品
					mccId: 0, // 分类Id
					model: null, // 型号
					name: null, // 名称
					original: null, // 原价
					pic: null, // 商品大图
					price: null, // 价格
					sizes: null, // 尺码
					slideshow: [], // 轮播
					standard: null, // 规格
					stock: null, // 库存
					userId: uni.getStorageSync( 'userInfo' ).userId // 用户id
				},
				fromPic: null,
				type: null,
				goodsId: null,
				fromZfbPic: null,
				fromWxPic: null,
				rewards: null
			};
		},
		onShow() {},
		onLoad( option ) {
			this.type = option.type;
			if ( option.goodsId ) {
				this.goodsId = option.goodsId;
				this.getData();
			}
		},
		methods: {
			change( e ) {
				this.from.classifyId = e;
			},
			savaInfo() {
				switch ( this.type ) {
					case 'add':
						this.add();
						break;
					case 'edit':
						this.edit();
						break;
				}
			},
			add() {
				this.http( {
					data: {
						a_d: this.$rsa.encrypt( this.from, true ),
						a_m: 'merchant_merchantCommodityAdd'
					},
					success: res => {
						this.$api.msg( res.message );
					}
				} );
			},
			edit() {
				this.http( {
					data: {
						a_d: this.$rsa.encrypt( this.from, true ),
						a_m: 'merchant_merchantCommodityUpdate'
					},
					success: res => {
						this.$api.msg( res.message );
					}
				} );
			},
			getData() {
				this.http( {
					data: {
						a_d: this.$rsa.encrypt( {
							id: this.goodsId,
							userId: uni.getStorageSync( 'userInfo' ).userId,
							limit: 1,
							offset: 1
						}, true ),
						a_m: 'merchant_merchantCommodityList'
					},
					success: res => {
						this.from = res.data.rows[ 0 ];
						this.from.userId = uni.getStorageSync( 'userInfo' ).userId

					}
				} );
			},
			imgUpload( item ) {
				let that = this;
				uni.chooseImage( {
					count: 1,
					sizeType: [ 'original', 'compressed' ],
					sourceType: [ 'album', 'camera' ],
					success( res ) {
						uni.uploadFile( {
							url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
							filePath: res.tempFilePaths[ 0 ], // 图片本地路径
							name: 'file', // 固定
							success: res => {
								that.from[ item ] = JSON.parse( res.data ).data[ 0 ].fileName;
							}
						} );
					}
				} );
			},
			select( e ) {
				// const tempFilePaths = e.tempFiles[0].file;
				// 微信小程序上传-需要微信临时提供临时地址
				const tempFilePaths = e.tempFilePaths;
				uni.uploadFile( {
					url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
					filePath: tempFilePaths[ 0 ],
					name: 'file',
					// 成功函数
					success: ( res ) => {
						// 取到文档服务器的值
						let uploaddata = JSON.parse( res.data ).data[ 0 ]
						console.log( uploaddata );
						let x = {}
						// 下面3个值是uni-app规定的一个不能少
						x.url = this.$options.filters[ 'jointPic' ]( uploaddata.fileName )
						x.name = uploaddata.sourceTitle
						this.from.slideshow.push( x )
						console.log( this.from.slideshow );
					},
					// 失败提示用户重新上传
					fail: error => {
						console.log( '失败', error );
					}
				} )
			},
		}
	};
</script>

<style lang="scss">
	.container {
		display: block;
		padding-top: 30rpx;
		padding-bottom: 200rpx;
	}

	.ui-all {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 0 auto;

		.ui-all-title {
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			font-weight: 600;
		}
	}

	.ui1-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;
	}

	.ui-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;
	}

	.pic-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto;

		// text-align: center;
		.pic-box-box {
			display: flex;
			justify-content: space-between;

			.imgAvatar {
				width: 40%;

				.iavatar {
					width: 100%;
					height: 200rpx;
					border-radius: 10rpx;
				}

				.iavatars {
					width: 100%;
					height: 200rpx;
				}
			}
		}

		.imgAvatars {
			width: 100%;

			.imgAvatars-iavatar {
				width: 100%;
			}

			.imgAvatars-iavatars {
				width: 200rpx;
			}
		}
	}

	.uifrom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20upx 0;
		font-size: 28upx;

		.uifrom-time {
			display: flex;
			align-items: center;
			width: 70%;

			text {
				padding: 0 30rpx;
			}
		}

		input {
			width: 70%;
			font-size: 28upx;
		}

		.section {
			width: 70%;
			font-size: 28upx;
		}

		textarea {
			width: 70%;
			font-size: 28upx;
		}

		.uifrom-btn {
			background: #148744;
			color: #ffffff;
			font-size: 28rpx;
			border-radius: 5upx;
			padding: 6rpx 28rpx;
		}
	}

	.uifrom-save {
		width: 100%;
		background-color: #ffffff;
		position: fixed;
		bottom: 0;
		padding: 30rpx 0;
		box-shadow: 0px -1px 3px 0px #dadada;

		.save {
			background-color: $base-color;
			color: #ffffff;
			margin-top: 40rpx auto;
			font-size: 28rpx;
			width: 95vw;
			border-radius: 10rpx;
		}
	}
</style>