<template>
	<view class="container">
		<view class="ui-all">
			<view class="ui-all-title">填写营业执照上的注册名称</view>
			<!-- <view class="uifrom">
				<view>店铺名称</view>
				<input type="text" placeholder="商户名称必填" v-model="from.merchantName" />
			</view> -->
			<view class="uifrom">
				<view>门店地址</view>
				<!-- <input type="text" placeholder="请输入门店地址" v-model="from.storeAddress" @click="getTude" /> -->
				<input type="text" placeholder="请输入门店地址" v-model="from.storeAddress" />
			</view>
			<view class="uifrom">
				<view>营业时间</view>
				<view class="uifrom-time">
					<picker mode="time" :value="startTime" start="09:01" end="21:01" @change="startTimeChange">
						<view class="uni-input" v-if="startTime">{{ startTime }}</view>
						<view class="uifrom-btn" v-else>开始时间</view>
					</picker>
					<text>至</text>
					<picker mode="time" :value="endTime" start="09:01" end="21:01" @change="endTimeChange">
						<view class="uni-input" v-if="endTime">{{ endTime }}</view>
						<view class="uifrom-btn" v-else>结束时间</view>
					</picker>
				</view>
			</view>
			<view class="uifrom">
				<view>门店分类</view>
				<uni-section title="配置左侧标题" type="line" style="width: 70%;">
					<uni-data-select v-model="from.classifyId" :localdata="typeRang" @change="change" placeholder="选择门店分类" :clear="false"></uni-data-select>
				</uni-section>
			</view>
			<!-- <view class="uifrom uifrom-end">
				<view>抖音视频口令</view>
				<textarea placeholder="请输入抖音视频口令" v-model="from.tikTokAddress"></textarea>
			</view> -->
		</view>
		<!-- <view class="ui1-box">
			<view class="uifrom">
				<view>联系电话</view>
				<input type="text" placeholder="请输入您的电话号码" v-model="from.userMobile" />
			</view>
			<view class="uifrom">
				<view>联系人</view>
				<input type="text" placeholder="请输入联系人的名字" v-model="from.userName" />
			</view>
		</view> -->
		<view class="ui1-box">
			<view class="uifrom b-b">
				<view>缴费保证金</view>
				<input type="text" placeholder="请输入缴费保证金额" v-model="from.deposit" />
			</view>
			<view class="uibtn"><button class="save" @tap="savaPay()">确认缴费</button><button class="save" @tap="recallPay()">撤回</button></view>
		</view>
		<!-- <view class="pic-box">
			<view class="uifrom">营业执照正面照片</view>
			<view class="imgAvatars" @tap="avatarChoose(1)">
				<image class="imgAvatars-iavatar" v-if="businessLicenseFront" :src="businessLicenseFront | jointPic" mode="widthFix"></image>
				<image class="imgAvatars-iavatars" v-else src="https://boshi.channce.com/imgs/yxstatic/icon/add.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="pic-box">
			<view class="uifrom">身份证号正面照片</view>
			<view class="imgAvatars" @tap="avatarChoose(2)">
				<image class="imgAvatars-iavatar" v-if="idCardFront" :src="idCardFront | jointPic" mode="widthFix"></image>
				<image class="imgAvatars-iavatars" v-else src="https://boshi.channce.com/imgs/yxstatic/icon/add.png" mode="widthFix"></image>
			</view>
		</view> -->
		<view class="pic-box">
			<view class="uifrom">门店图片</view>
			<view class="imgAvatars" @tap="avatarChoose(3)">
				<image class="imgAvatars-iavatar" v-if="storePicture" :src="storePicture | jointPic" mode="widthFix"></image>
				<image class="imgAvatars-iavatars" v-else src="https://boshi.channce.com/imgs/yxstatic/icon/add.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="uifrom-save"><button class="save" @tap="savaInfo()">确 认 申 请</button></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				from: {
					storeAddress: '',
					classifyId: 0
				},
				businessLicenseFront: null,
				idCardFront: null,
				storePicture: null,
				startTime: '',
				endTime: '',
				value: '0',
				typeRang: []
			};
		},
		onShow() {
			this.getType();
		},
		methods: {
			getType() {
				this.http({
					url: '/api/bdc/ct',
					method: 'post',
					success: res => {
						this.typeRang = res.data;
						for (var i = 0; i < res.data.length; i++) {
							this.typeRang[i].text = res.data[i].classifyName;
							this.typeRang[i].value = res.data[i].id;
						}
					}
				});
				this.http({
					url: '/api/bdm/mi',
					method: 'post',
					data: { userId: uni.getStorageSync('userInfo').userId },
					success: res => {
						this.from = res.data;
						this.from.classifyId = parseInt(res.data.classifyId);
						this.businessLicenseFront = res.data.businessLicenseFront;
						this.idCardFront = res.data.idCardFront;
						this.storePicture = res.data.storePicture;
					}
				});
			},
			change(e) {
				this.from.classifyId = e;
			},
			getTude() {
				let that = this;
				uni.chooseLocation({
					success: function(res) {
						// console.log('位置名称：' + res.name);
						// console.log('详细地址：' + res.address);
						// console.log('纬度：' + res.latitude);
						// console.log('经度：' + res.longitude);
						that.from.storeAddress = `${res.address}${res.name}`;
						that.from.storeLatitude = res.latitude;
						that.from.storeLongitude = res.longitude;
					}
				});
			},
			startTimeChange(e) {
				this.startTime = `${e.detail.value}:00`;
			},
			endTimeChange(e) {
				this.endTime = `${e.detail.value}:00`;
			},
			savaInfo() {
				this.http({
					url: '/api/bdm/mm',
					method: 'post',
					data: {
						businessLicenseFront: this.businessLicenseFront,
						classifyId: this.from.classifyId,
						idCardFront: this.idCardFront,
						merchantName: this.from.merchantName,
						storeAddress: this.from.storeAddress,
						storeBusinessHours: `${this.startTime} - ${this.endTime}`,
						storeLatitude: '',
						storeLongitude: '',
						storePicture: this.storePicture,
						tikTokAddress: this.from.tikTokAddress,
						userId: uni.getStorageSync('userInfo').userId,
						userMobile: this.from.userMobile,
						userName: this.from.userName
					},
					success: res => {
						this.$api.msg(res.message);
					}
				});
			},
			savaPay() {
				this.http({
					url: '/api/bdm/mpd',
					method: 'post',
					data: {
						merchantId: this.from.id,
						payDeposit: this.from.deposit
					},
					success: res => {
						this.$api.msg(res.message);
						this.getType()
					}
				});
			},
			recallPay() {
				this.http({
					url: '/api/bdm/mrd',
					method: 'post',
					data: {
						merchantId: this.from.id,
						refundDeposit: this.from.deposit
					},
					success: res => {
						this.$api.msg(res.message);
						this.getType()
					}
				});
			},

			avatarChoose(item) {
				this.picType = item;
				let that = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success(res) {
						// tempFilePath可以作为img标签的src属性显示图片
						that.imgUpload(res.tempFilePaths);
						const tempFilePaths = res.tempFilePaths;
					}
				});
			},
			imgUpload(file) {
				uni.uploadFile({
					url: 'https://boshi.channce.com/boshi/fe/fileUpload', //接口地址
					filePath: file[0], // 图片本地路径
					name: 'file', // 固定
					success: res => {
						if (this.picType == 1) {
							this.businessLicenseFront = JSON.parse(res.data).data[0].fileName;
						} else if (this.picType == 2) {
							this.idCardFront = JSON.parse(res.data).data[0].fileName;
						} else {
							this.storePicture = JSON.parse(res.data).data[0].fileName;
						}
					},
					fail: err => {}
				});
			}
		}
	};
</script>

<style lang="scss">
	.container {
		display: block;
		padding-top: 30rpx;
		padding-bottom: 200rpx;
	}

	.ui-all {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 0 auto;

		.ui-all-title {
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			font-weight: 600;
		}
	}

	.ui1-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;
	}

	.ui-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto 0;
	}

	.pic-box {
		width: 95vw;
		background-color: #ffffff;
		padding: 10upx 40upx;
		border-radius: 20upx;
		margin: 30upx auto;

		// text-align: center;
		.pic-box-box {
			display: flex;
			justify-content: space-between;

			.imgAvatar {
				width: 40%;

				.iavatar {
					width: 100%;
					height: 200rpx;
					border-radius: 10rpx;
				}

				.iavatars {
					width: 100%;
					height: 200rpx;
				}
			}
		}

		.imgAvatars {
			width: 100%;

			.imgAvatars-iavatar {
				width: 100%;
			}

			.imgAvatars-iavatars {
				width: 200rpx;
			}
		}
	}

	.b-b {
		border-bottom: 1px solid #dadada;
	}

	.uifrom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20upx 0;
		font-size: 28upx;

		.uifrom-time {
			display: flex;
			align-items: center;
			width: 70%;

			text {
				padding: 0 30rpx;
			}
		}

		input {
			width: 70%;
			font-size: 28upx;
		}

		.uifrom-btn {
			background: #148744;
			color: #ffffff;
			font-size: 28rpx;
			border-radius: 5upx;
			padding: 6rpx 28rpx;
		}
	}

	.uifrom-end {
		align-items: flex-start;

		textarea {
			width: 70%;
			height: 60px;
			font-size: 28upx;
			border: 1px solid #e5e5e5;
			border-radius: 4px;
			padding: 5px;
		}
	}

	.uibtn {
		text-align: end;
		padding-top: 10rpx;

		button {
			background: #148744;
			color: #ffffff;
			font-size: 28rpx;
			border-radius: 5upx;
			height: 60rpx;
			line-height: 60rpx;
			display: inline-flex;
			margin-left: 10rpx;
		}
	}

	.uifrom-save {
		width: 100%;
		background-color: #ffffff;
		position: fixed;
		bottom: 0;
		padding: 30rpx 0;
		box-shadow: 0px -1px 3px 0px #dadada;

		.save {
			background: #148744;
			color: #ffffff;
			margin-top: 40rpx auto;
			font-size: 28rpx;
			width: 95vw;
			border-radius: 50upx;
		}
	}
</style>