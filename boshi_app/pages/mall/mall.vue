<template>
  <view class="page">
    <view class="page-title">
      <uni-search-bar
        class="uni-mt-10"
        radius="100"
        placeholder="大家都在搜“沙滩短裤”"
        clearButton="none"
        cancelButton="none"
        @confirm="search"
      />
      <image
        class="title-icon"
        src="/static/mall/cart.png"
        mode="widthFix"
        @click="navTo('/pages/cart/cart')"
      ></image>
      <image
        class="title-icon"
        src="/static/mall/order.png"
        mode="widthFix"
        @click="navTo('/pages/order/order')"
      ></image>
      <image
        class="title-icon"
        src="/static/mall/more.png"
        mode="widthFix"
      ></image>
    </view>
    <!-- 轮播图板块 -->
    <view class="page-bannar">
      <swiper
        class="pageBannarSwiper"
        circular
        indicator-dots="true"
        style=""
        autoplay="5000"
      >
        <swiper-item
          class="pageBannarSwiper-item"
          v-for="(item, index) in bannarList"
          :key="index"
        >
          <image src="/static/mall/<EMAIL>" mode="widthFix"></image>
        </swiper-item>
      </swiper>
    </view>

    <view class="category-list">
      <view
        class="category-item"
        v-for="(item, index) in tripartiteList"
        :key="index"
      >
        <image :src="item.pic" mode="widthFix"></image>
        <view>{{ item.name }}</view>
      </view>
    </view>

    <view class="goodList">
      <view
        class="goodList-item"
        v-for="item in goodsList"
        :key="item.id"
        @click="goProduct(item.id)"
      >
        <image src="/static/mall/icon1@3x(2).png" mode="widthFix"></image>
        <view class="right">
          <view class="top">{{ item.name }}</view>
          <view class="bottom">
            <text class="money">￥{{ item.money }}</text>
            <text class="btn">到手价</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Notice from "@/components/notice.vue";
import empty from "@/components/empty.vue";

export default {
  components: { Notice, empty },
  data() {
    return {
      bannarList: [],
      tripartiteList: [
        {
          name: "数码",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/product/list?id=2&name=积分商城",
        },
        {
          name: "配饰",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/product/list?id=1&name=生活商城",
        },
        {
          name: "服饰",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/house/house",
        },
        {
          name: "亏本甩卖",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/house/house",
        },
        {
          name: "限时折扣",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/userMall/userMall",
        },
        {
          name: "爱宠之家",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/userMall/userMall",
        },
        {
          name: "夏季水果",
          pic: "/static/mall/icon1@3x(1).png",
          navTo: "/pages/userMall/userMall",
        },
      ],
      goodsList: [],
    };
  },
  onLoad() {
    this.getData();
    this.getGoodsList();
  },
  methods: {
    getData() {
      // 获取bannar图
      this.http({
        url: "/api/advert/carouselPictures",
        success: (res) => {
          this.bannarList = res.data;
        },
      });
      // 获取公告
      this.http({
        url: "/api/usersMessage/list",
        method: "post",
        data: { limit: 5, offset: 1, messageType: 1 },
        success: (res) => {
          this.messageList = res.data.rows;
        },
      });
    },
    goLink(item) {
      console.log(item.jumpUrl.split("://"));
      if (item.jumpUrl != "") {
        let url = item.jumpUrl.split("://");
        if (url[0] == "appPath") {
          uni.navigateTo({ url: url[1] });
          return;
        }
        // #ifdef APP-PLUS
        plus.runtime.openURL(item.jumpUrl);
        // #endif
        // #ifdef H5
        window.open(item.jumpUrl);
        // #endif
      }
    },
    goProduct(goodsId) {
      // uni.navigateTo({ url:  });
      let urls =
        "https://boshi.channce.com/shop_boshi/#" +
        `/pages/product/product?goodsId=${goodsId}`;
      console.log(urls);
      window.webkit.messageHandlers.pushNewWebVC.postMessage({
        navtitle: "铂时",
        url: urls,
      });
    },
    navTo(item) {
      let urls = "https://boshi.channce.com/shop_boshi/#" + item;
      window.webkit.messageHandlers.pushNewWebVC.postMessage({
        navtitle: "铂时",
        url: urls,
      });
      return;
      if (item == "/pages/house/house" || item == "/pages/userMall/userMall") {
        uni.switchTab({ url: item });
      } else {
        uni.navigateTo({ url: item });
      }
    },
    getHotGoods() {
      this.http({
        url: "/api/ssc/scList",
        data: { isTj: 1, limit: 10, offset: 1 },
        success: (res) => {
          this.hotGoodsList = res.data.rows;
        },
      });
    },
    getPlural() {
      let that = this;
      that.http({
        url: "/api/sp/spl",
        method: "post",
        data: {},
        success: (res) => {
          that.pluralShopList = [];
          res.data.forEach((item, index) => {
            that.getGoodsList(item, index);
          });
        },
      });
    },
    getGoodsList(item, index) {
      this.http({
        url: "/api/ssc/scList",
        method: "post",
        data: {
          commodityClassifyLevel1: 0,
          commodityClassifyLevel2: 0,
          limit: 10,
          offset: 1,
          plural: 1,
        },
        success: (res) => {
          this.goodsList = res.data.rows;
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  background-color: #f8f9fa;
  background-image: url("~@/static/mall/indexBg.png");
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 60rpx;

  .page-title {
    height: 140rpx;
    width: 95vw;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10rpx;

    ::v-deep .uni-searchbar {
      flex: 1;
    }

    .title-icon {
      width: 80rpx;
    }
  }

  // 轮播图板块
  .page-bannar {
    .type {
      display: flex;
      align-items: center;
      width: 90vw;
      margin: 0 auto 10px;

      view {
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        text-decoration: none;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 40rpx;
        cursor: pointer;
        color: #ffffff;
        position: relative;
      }

      .is_li::after {
        content: "";
        background-color: #ffffff;
        height: 4rpx;
        width: 50%;
        position: absolute;
        bottom: 0px;
      }
    }

    .pageBannarSwiper {
      height: 340rpx;

      .pageBannarSwiper-item {
        text-align: center;

        image {
          width: 95%;
          height: 100%;
          border-radius: 10rpx;
        }

        .fullscreen-video {
          width: 95%;
          height: 100%;
          border-radius: 10rpx;
        }
      }
    }
  }

  .goodList {
    display: flex;
    flex-wrap: wrap;
    width: 95vw;
    margin: 10px auto 0;
    justify-content: space-between;
    gap: 10px;

    &::-webkit-scrollbar {
      display: none;
    }

    .goodList-item {
      color: #000000;
      font-size: 14px;
      padding: 20rpx;
      width: 48%;
      background-color: #ffffff;
      border-radius: 10px;

      image {
        width: 100%;
        height: 200rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
      }

      .right {
        padding-top: 20rpx;

        .top {
          font-size: 28rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #454545;
        }

        .bottom {
          display: flex;
          width: 100%;
          align-items: center;
          margin-top: 10rpx;

          .money {
            font-weight: 600;
            color: #de0000;
          }

          .btn {
            color: #b4c7e7;
            font-size: 24rpx;
            text-decoration: line-through;
            margin-left: 2px;
          }
        }
      }
    }
  }

  .category-list {
    display: flex;
    flex-wrap: wrap;
    width: 95vw;
    margin: 30rpx auto 0 auto;
    gap: 20rpx 0;

    .category-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20rpx;

      image {
        width: 80rpx;
        border-radius: 16rpx;
        background: #f5f5f5;
        margin-bottom: 10rpx;
      }

      view {
        font-size: 26rpx;
        color: #333;
        text-align: center;
        margin-top: 4rpx;
      }
    }
  }
}
</style>
