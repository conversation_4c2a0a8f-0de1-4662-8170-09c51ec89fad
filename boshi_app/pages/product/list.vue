<template>
	<view class="page">
		<!-- 搜索板块 -->
		<view class="page-search">
			<uni-search-bar placeholder="输入你要查找的内容" bgColor="#EEEEEE" radius="100" @confirm="search" :focus="false" v-model="searchValue" cancelButton="none"></uni-search-bar>
		</view>

		<view class="content" v-if="classifyList.length != 0">
			<scroll-view scroll-y class="left-aside">
				<view v-for="item in classifyList" :key="item.id" class="f-item" :class="{active: item.id === current}" @click="changeType(item)">
					{{ item.classifyName }}
				</view>
			</scroll-view>
			<scroll-view scroll-with-animation scroll-y class="right-aside">
				<view class="right_aside_goodList" v-if="goodsList.length != 0">
					<view class="goodList-item" v-for="(item, index) in goodsList" :key="index" @click="goPage(`/pages/product/product?goodsId=${item.id}`)">
						<image :src="item.showPic | jointPic" mode="aspectFill"></image>
						<view class="right">
							<view class="right_name">{{ item.name }}</view>
							<view class="right_msg">
								<view class="right_msg_left">
									<view v-if="item.gainCustoms">赠送 {{ item.gainCustoms }} 积分</view>
									<view class="money" v-if="item.plural != 2"> <text>￥</text> {{ item.money }}</view>
									<view class="money" v-if="item.plural == 2">{{ item.money }} <text>积分</text></view>
								</view>
								<view class="right_msg_button">选规格</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import noMore from "@/components/noMore.vue"
	export default {
		components: { noMore },
		data() {
			return {
				items: ['精选好物', '男装', '女装', '酒水', '饮料'],
				current: 0,
				tabScrollTop: 0,
				carouselList: [],
				searchValue: '',
				goodsList: [],
				classifyList: [],
				isTj: undefined
			};
		},
		onShow() {},
		onLoad(options) {
			if (options.isTj) {
				this.isTj = options.isTj
			}
			this.pluralId = options.id;
			if (decodeURIComponent(options.name) != 'undefined') {
				uni.setNavigationBarTitle({
					title: decodeURIComponent(options.name)
				});
			}
			this.getData();
		},
		methods: {
			getData() {
				this.http({
					url: '/api/advert/carouselPictures',
					method: 'post',
					data: {},
					success: res => { this.carouselList = res.data; },
				});
				this.http({
					url: '/api/cc/ccL',
					method: 'post',
					data: {
						classifyPid: 0,
					},
					success: res => {
						if (res.data != '') {
							this.classifyList = res.data;
							this.current = res.data[0].id;
						}
						this.loadData()
					}
				});
			},
			changeType(item) {
				this.current = item.id;
				this.goodsList = []
				this.loadData()
			},
			loadData() {
				this.http({
					url: '/api/ssc/scList',
					method: 'post',
					data: {
						commodityClassifyLevel1: this.current,
						commodityClassifyLevel2: 0,
						limit: 100,
						offset: 1,
						plural: this.pluralId,
						shareName: this.searchValue,
						isTj: this.isTj
					},
					success: res => {
						res.data.rows.forEach(item => {
							this.goodsList.push(item);
						});
					}
				});
			},
			goPage(route, data, fun) {
				uni.navigateTo({ url: route });
			},
			navToList(goodsId) {
				uni.navigateTo({
					url: `/pages/product/product?goodsId=${goodsId}`
				});
			},
			onClickItem(e) {
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
			},
			search() {
				this.goodsList = []
				this.loadData()
			},
			clear() {},

			//右侧栏滚动
			asideScroll(e) {
				if (!this.sizeCalcState) {
					this.calcSize();
				}
				let scrollTop = e.detail.scrollTop;
				let tabs = this.slist.filter(item => item.top <= scrollTop).reverse();
				if (tabs.length > 0) {
					this.currentId = tabs[0].pid;
				}
			},
			//计算右侧栏每个tab的高度等信息
			calcSize() {
				let h = 0;
				this.slist.forEach(item => {
					let view = uni.createSelectorQuery().select("#main-" + item.id);
					view.fields({
						size: true
					}, data => {
						item.top = h;
						h += data.height;
						item.bottom = h;
					}).exec();
				})
				this.sizeCalcState = true;
			},
		}
	};
</script>

<style lang="scss">
	.page {
		background-color: #fff;

		// 搜索板块
		.page-search {
			background-color: #fff;

			::v-deep .uni-searchbar {
				width: 100%;
			}
		}

		.selectTop-select {
			display: flex;
			align-items: center;
			width: 90vw;
			margin: 0 auto 0;

			view {
				font-size: 30rpx;
				color: #9F9F9F;
				display: flex;
				align-items: center;
				margin-right: 40rpx;
				padding-bottom: 10rpx;
			}

			.is_li {
				color: $uni-color-primary;
				font-weight: bold;
				position: relative;
			}

			.is_li::after {
				content: "";
				height: 4rpx;
				width: 30px;
				background: $uni-color-primary;
				border-radius: 16px;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translate(-50%);
			}
		}

		.commodity-module {
			width: 95vw;
			margin: 40rpx auto;

			.goodList {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.goodList-item {
					width: 48%;
					border-radius: 10px 10px 10px 10px;
					background-color: #ffffff;
					color: #000000;
					font-size: 14px;
					margin-bottom: 40upx;
					box-shadow: 0px 0px 5px -1px #a7a7a7;

					image {
						width: 100%;
						height: 350rpx;
						border-radius: 10px 10px 0px 0px;
					}

					.right {
						width: 100%;
						padding: 10rpx 20rpx;

						.top {
							font-size: 30rpx;
							font-weight: 600;
							display: -webkit-box;
							overflow: hidden;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}

						.top-box {
							display: inline-block;
							font-size: 12px;
							color: #af9029;
							border: 1px solid #af9029;
							border-radius: 5rpx;
							padding: 0 10rpx;
						}

						.bottom {
							display: flex;
							width: 100%;
							align-items: center;
							padding-top: 5rpx;

							.money {
								color: rgba(236, 65, 19, 1);
								font-size: 28rpx;
							}

							.btn {
								color: rgba(171, 168, 168, 0.88);
								font-size: 24rpx;
								text-decoration: line-through;
								margin-left: 2px;
							}
						}
					}
				}
			}
		}

		.content {
			display: flex;
			height: calc(100vh - 200rpx);
			background-color: #ffffff;
			overflow: hidden;

			.left-aside {
				flex-shrink: 0;
				width: 200upx;
				height: 100%;
				background: #f5f5f5;

				.f-item {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;
					height: 100upx;
					font-size: 28upx;
					color: $font-color-base;
					position: relative;

					&.active {
						color: $base-color;
						background-color: #fff;

						&:before {
							content: '';
							position: absolute;
							left: 0;
							top: 50%;
							transform: translateY(-50%);
							height: 36upx;
							width: 8upx;
							background-color: $base-color;
							border-radius: 0 4px 4px 0;
							opacity: .8;
						}
					}
				}
			}

			.right-aside {
				flex: 1;
				overflow: hidden;
				height: 100%;
				background: #fff;

				.right_aside_goodList {
					padding: 20rpx 15rpx;

					.goodList-item {
						width: 100%;
						color: #000000;
						font-size: 14px;
						margin-bottom: 40upx;
						display: flex;

						image {
							width: 170rpx;
							height: 170rpx;
							margin-right: 20rpx;
						}

						.right {
							width: calc(100% - 210rpx);
							height: 170rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;

							.right_name {
								font-size: 26rpx;
								display: -webkit-box;
								overflow: hidden;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
							}

							.right_msg {
								display: flex;
								align-items: flex-end;
								justify-content: space-between;
								font-size: 24rpx;

								.sales {
									color: #999999;
								}

								.money {
									color: rgba(236, 65, 19, 1);
									font-size: 36rpx;

									text {
										font-size: 24rpx;
									}
								}

								.right_msg_button {
									background: #FF3000;
									border-radius: 30rpx;
									color: #FFFFFF;
									width: 100rpx;
									height: 44rpx;
									display: flex;
									justify-content: center;
									align-items: center;
								}
							}
						}
					}
				}
			}
		}
	}
</style>
