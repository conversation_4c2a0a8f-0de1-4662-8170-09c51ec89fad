<template>
	<view class="page">
		<!-- 搜索板块 -->
		<view class="page-search">
			<uni-search-bar placeholder="输入你要查找的内容" bgColor="#EEEEEE" radius="100" @confirm="search" :focus="false" v-model="searchValue" cancelButton="none"></uni-search-bar>
		</view>
		<!-- 热销榜单 -->
		<view class="commodity-module">
			<view class="goodList">
				<view class="goodList-item" v-for="(item, index) in goodsList" :key="index" @click="goPage('/pages/product/product', `goodsId=${item.id}`, 1)">
					<image :src="item.showPic | jointPic" mode="aspectFill"></image>
					<view class="right">
						<view class="top">{{ item.name }}</view>
						<view class="top-box" v-if="item.gainCustoms">赠送 {{ item.gainCustoms }} 积分</view>
						<view class="bottom">
							<view class="money">￥{{ item.money }}</view>
							<!-- <view class="btn">月销1万</view> -->
						</view>
					</view>
				</view>
			</view>
			<luanqing-empty :show="goodsList.length == 0" text="没有更多数据啦" textColor="#000"></luanqing-empty>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				current: 0,
				searchValue: '',
				goodsList: []
			};
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			loadData() {
				this.http({
					url: '/api/ssc/scList',
					method: 'post',
					data: {
						commodityClassifyLevel1: 0,
						commodityClassifyLevel2: 0,
						limit: 100,
						offset: 1,
						plural: 0,
						shareName: this.searchValue
					},
					success: res => {
						res.data.forEach(item => {
							this.goodsList.push(item);
						});
					}
				});
			},
			goPage(route, data, fun) {
				uni.navigateTo({
					url: `${route}?${data}`
				});
			},
			search() {
				this.goodsList = []
				this.loadData()
			},
			clear() {}
		}
	};
</script>

<style lang="scss">
	.page {

		// 搜索板块
		.page-search {
			background-color: #fff;
			position: fixed;
			width: 100vw;

			z-index: 99;
			// #ifdef APP-PLUS
			top: 0;
			// #endif
			// #ifdef H5
			top: 88rpx;
			// #endif

			::v-deep .uni-searchbar {
				width: 100%;
			}
		}

		// 轮播图
		.carousel {
			width: 90%;
			height: 350upx;
			margin: 0 auto 40rpx;

			.carousel-item {
				width: 100%;
				height: 100%;
				padding: 0;
			}

			image {
				width: 100%;
				height: 100%;
				border-radius: 10px;
			}
		}

		.commodity-module {
			width: 95vw;
			margin: 150rpx auto;

			.goodList {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.goodList-item {
					width: 48%;
					border-radius: 10px 10px 10px 10px;
					background-color: #ffffff;
					color: #000000;
					font-size: 14px;
					margin-bottom: 40upx;
					box-shadow: 0px 0px 5px -1px #a7a7a7;

					image {
						width: 100%;
						height: 350rpx;
						border-radius: 10px 10px 0px 0px;
					}

					.right {
						width: 100%;
						padding: 10rpx 20rpx;

						.top {
							font-size: 30rpx;
							font-weight: 600;
							display: -webkit-box;
							overflow: hidden;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}

						.top-box {
							display: inline-block;
							font-size: 12px;
							color: #af9029;
							border: 1px solid #af9029;
							border-radius: 5rpx;
							padding: 0 10rpx;
						}

						.bottom {
							display: flex;
							width: 100%;
							align-items: center;
							padding-top: 5rpx;

							.money {
								color: rgba(236, 65, 19, 1);
								font-size: 28rpx;
							}

							.btn {
								color: rgba(171, 168, 168, 0.88);
								font-size: 24rpx;
								text-decoration: line-through;
								margin-left: 2px;
							}
						}
					}
				}
			}
		}
	}
</style>