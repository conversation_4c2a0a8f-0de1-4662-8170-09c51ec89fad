<template>
	<view>
		<!-- 地址 -->
		<navigator url="/pages_set/address/address?source=1" class="address-section">
			<view class="order-content">
				<text class="yticon icon-shouhuodizhi"></text>
				<view class="cen" v-if="addressData.id">
					<view class="top">
						<text class="name">{{ addressData.name }}</text>
						<text class="mobile">{{ addressData.mobile }}</text>
					</view>
					<text class="address">{{ addressData.address }}</text>
				</view>
				<view class="cen" v-else>未选择地址</view>
				<text class="yticon icon-you"></text>
			</view>
		</navigator>

		<view class="goods-section">
			<!-- 商品列表 -->
			<view class="g-item" v-for="(item, index) in detail" :key="index">
				<image :src="item.showPic | jointPic"></image>
				<view class="right">
					<text class="title clamp">{{ item.name }}</text>
					<text class="spec">{{ item.orderStandard }}</text>
					<text class="spec">抵扣余额：{{ item.money1 }}</text>
					<view class="price-box">
						<view class="price" v-if="detail[0].plural == 1">￥{{ item.money }}</view>
						<view class="price" v-if="detail[0].plural == 2">{{ item.money }}积分</view>
						<view class="number">x {{ item.count }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 金额明细 -->
		<view class="money">
			<view class="money-item ">
				<text class="item-tit clamp">商品金额</text>
				<view class="item-tip" v-if="detail[0].plural == 1">￥{{ money.a  }}</view>
				<view class="item-tip" v-if="detail[0].plural == 2">{{ money.a  }} 积分</view>
			</view>
			<!-- <view class="money-item  pr0" v-if="detail[0].plural == 2">
				<text class="item-tit clamp">是否积分抵扣</text>
				<switch color="#FFCC33" style="transform:scale(0.7)" @change="switch1Change" />
			</view> -->
			<view class="money-item">
				<text class="item-tit clamp">运费</text>
				<text class="item-tip">￥{{ money.c }}</text>
			</view>
			<view class="money-item ">
				<text class="item-tit clamp">合计：</text>
				<text class="item-tip">
					<text class="item-allmoney red" v-if="detail[0].plural == 1">￥{{money.b }}</text>
					<text class="item-allmoney red" v-if="detail[0].plural == 2">{{money.b }} 积分</text>
				</text>
			</view>
		</view>

		<!-- 底部 -->
		<view class="footer">
			<view class="price-content" v-if="detail[0].plural == 1">合计：￥{{ money.b }}</view>
			<view class="price-content" v-if="detail[0].plural == 2">合计：{{ money.b }} 积分</view>
			<text class="submit" @click="submit">提交订单</text>
		</view>

	</view>
</template>

<script>
	import { mapMutations } from 'vuex';
	export default {
		data() {
			return {
				maskState: 0, //消费券面板显示状态
				desc: '', //备注
				payType: 1, //1微信 2支付宝
				addressData: {},
				detail: this.$store.state.detail,
				isDeduction: 1,
				actual: '',
				total: '',
				deductionTotal: '',
				pageType: "",
				money: {},
				uuid: ""
			};
		},
		onShow() {
			this.uuid = ''
			this.userId = uni.getStorageSync('userInfo').userId
			for (let i = 0; i < this.detail.length; i++) {
				this.uuid += this.detail[i].uuid + ',';
				this.alltotal += this.detail[i].count;
			}
			this.getAddress();
			this.actual = this.total;
		},
		onLoad(options) {
			// 路由获取商品分区ID
			this.payId = options.payId;
			// 是否是购物车
			this.pageType = options.type;
		},
		methods: {
			...mapMutations(['addOrder', 'delOrder']),
			switch1Change(e) {
				if (e.detail.value) {
					this.isDeduction = 1;
				} else {
					this.isDeduction = 0;
				}
				this.calcTotal()
			},
			getAddress() {
				this.http({
					data: { a_m: 'address_uAddG', a_d: this.$rsa.encrypt({ uid: this.userId }, true) },
					success: res => {
						if (JSON.stringify(res.data) != "{}") {
							this.addressData = res.data;
							this.calcTotal()
						}
					}
				});
			},
			calcTotal() {
				if (!this.addressData) {
					this.addressData.id = 0
				}
				switch (this.pageType) {
					case 'cart':
						this.http({
							data: { a_m: 'cart_cpPayCartMoney', a_d: this.$rsa.encrypt({ addId: this.addressData.id, uuid: this.uuid, userId: this.userId, disCountWay: this.isDeduction }, true) },
							success: res => { this.money = res.data }
						});
						break;
					case 'buy':
						this.http({
							data: {
								a_m: 'so_cpPayMoney',
								a_d: this.$rsa.encrypt({ addId: this.addressData.id, id: this.detail[0].id, tradeNumber: this.detail[0].count, userId: this.userId, disCountWay: this.isDeduction }, true),
							},
							success: res => { this.money = res.data }
						});
						break;
				}
			},
			submit() {
				if (this.addressData == null) {
					this.$api.msg('请添加收货地址')
					return
				}
				let order = {}
				let urls;
				if (this.pageType == 'cart') {
					order.addId = this.addressData.id;
					order.disCountWay = this.isDeduction;
					order.uuid = this.uuid;
					order.actualAmount = this.money.b
					this.addOrder(order);
					urls = `/pages/money/pay?type=cart&payId=${this.detail[0].plural}`;
				} else {
					this.detail[0].addId = this.addressData.id;
					this.detail[0].actualAmount = this.money.b
					this.detail[0].disCountWay = this.isDeduction;
					this.addOrder(this.detail[0]);
					urls = `/pages/money/pay?type=buy&payId=${this.detail[0].plural}`;
				}
				uni.navigateTo({ url: urls });
			},
		}
	};
</script>

<style lang="scss">
	page {
		background: $page-color-base;
		padding-bottom: 100upx;
	}

	.address-section {
		.order-content {
			padding: 30upx 0;
			display: flex;
			align-items: center;
			background: #fff;
			width: 95vw;
			margin: 10px auto;
			border-radius: 10upx;
		}

		.icon-shouhuodizhi {
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90upx;
			color: #888;
			font-size: 44upx;
		}

		.cen {
			display: flex;
			flex-direction: column;
			flex: 1;
			font-size: 28upx;
			color: $font-color-dark;
		}

		.name {
			font-size: 34upx;
			margin-right: 24upx;
		}

		.address {
			margin-top: 16upx;
			margin-right: 20upx;
			color: $font-color-light;
		}

		.icon-you {
			font-size: 32upx;
			color: $font-color-light;
			margin-right: 30upx;
		}

		.a-bg {
			position: absolute;
			left: 0;
			bottom: 0;
			display: block;
			width: 100%;
			height: 5upx;
		}
	}

	.goods-section {
		background: #fff;
		width: 95vw;
		margin: 20upx auto;
		border-radius: 10upx;
		padding: 20upx 0;

		.g-header {
			display: flex;
			align-items: center;
			height: 84upx;
			padding: 0 30upx;
			position: relative;
		}

		.logo {
			display: block;
			width: 50upx;
			height: 50upx;
			border-radius: 100px;
		}

		.name {
			font-size: 30upx;
			color: $font-color-base;
			margin-left: 24upx;
		}

		.g-item {
			display: flex;
			margin: 10rpx 20upx;
			align-items: end;

			image {
				flex-shrink: 0;
				display: block;
				width: 140upx;
				height: 140upx;
				border-radius: 8upx;
			}

			.right {
				height: 140upx;
				flex: 1;
				padding-left: 24upx;
				overflow: hidden;
				position: relative;
			}

			.title {
				font-size: 30upx;
				font-weight: 600;
				color: $font-color-dark;
			}

			.spec {
				font-size: 26upx;
				color: $font-color-light;
			}

			.price-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
				justify-items: end;
				font-size: 32upx;
				color: $font-color-dark;
				padding-top: 10upx;
				position: absolute;
				bottom: 0px;
				width: 90%;

				.price {
					margin-bottom: 4upx;
				}

				.number {
					font-size: 26upx;
					color: $font-color-base;
					margin-left: 20upx;
				}
			}

			.step-box {
				position: relative;
			}
		}
	}


	.money {
		width: 95vw;
		background: #fff;
		margin: 20upx auto;
		border-radius: 10upx;
		padding: 10upx 0;

		.money-item {
			display: flex;
			align-items: center;
			padding: 0 40upx;
			line-height: 70upx;

			&.pr0 {
				padding-right: 0;
			}

			.cell-more {
				align-self: center;
				font-size: 24upx;
				color: $font-color-light;
				margin-left: 8upx;
				margin-right: -10upx;
			}

			.item-tit {
				flex: 1;
				font-size: 26upx;
				margin-right: 10upx;
			}


			.item-tip {
				font-size: 26upx;
				color: $font-color-dark;

				&.disabled {
					color: $font-color-light;
				}

				&.active {
					color: $base-color;
				}
			}

			.red {
				color: $base-color;
				font-weight: 600;
			}

			.item-allmoney {
				font-size: 40rpx;

			}


			.desc {
				flex: 1;
				font-size: $font-base;
				color: $font-color-dark;
			}
		}
	}

	.yt-list {
		width: 95vw;
		background: #fff;
		margin: 20upx auto;
		border-radius: 10upx;
		padding: 10upx 0;
	}

	.yt-list-cell {
		display: flex;
		align-items: center;
		padding: 0 40upx;
		line-height: 70upx;
		// position: relative;

		// &.cell-hover {
		// 	background: #fafafa;
		// }

		&.b-b:after {
			left: 30upx;
		}

		.cell-icon {
			height: 32upx;
			width: 32upx;
			font-size: 22upx;
			color: #fff;
			text-align: center;
			line-height: 32upx;
			background: #f85e52;
			border-radius: 4upx;
			margin-right: 12upx;

			&.hb {
				background: #ffaa0e;
			}

			&.lpk {
				background: #3ab54a;
			}
		}

		.cell-more {
			align-self: center;
			font-size: 24upx;
			color: $font-color-light;
			margin-left: 8upx;
			margin-right: -10upx;
		}

		.cell-tit {
			flex: 1;
			font-size: 26upx;
			color: $font-color-light;
			margin-right: 10upx;
		}

		.cell-tip {
			font-size: 26upx;
			color: $font-color-dark;

			&.disabled {
				color: $font-color-light;
			}

			&.active {
				color: $base-color;
			}

			&.red {
				color: $base-color;
			}
		}

		&.desc-cell {
			.cell-tit {
				max-width: 90upx;
			}
		}

		.desc {
			flex: 1;
			font-size: $font-base;
			color: $font-color-dark;
		}
	}

	.pay-item {
		width: 95vw;
		background: #fff;
		margin: 20upx auto;
		border-radius: 10upx;
		padding: 10upx 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-right: 20upx;
		line-height: 1;
		height: 110upx;
		padding: 0 20px;

		.con {
			flex: 1;
			font-size: 13px;
			margin-right: 5px;
		}
	}

	.pay-item::v-deep .uni-checkbox-input {
		border-radius: 50%;
	}

	.pay-item::v-deep .uni-checkbox-input-checked {
		background-color: rgb(250, 67, 106);
		border-color: rgb(250, 67, 106);
	}

	.pay-item::v-deep .uni-checkbox-input:hover {
		border-radius: 50%;
		border-color: #d1d1d1;
	}

	.footer {
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 995;
		display: flex;
		align-items: center;
		width: 100%;
		height: 90upx;
		justify-content: space-between;
		font-size: 30upx;
		background-color: #fff;
		z-index: 998;
		color: $font-color-base;
		box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);

		.price-content {
			padding-left: 30upx;
			font-size: 36upx;
			color: $uni-color-primary;
		}

		.price-tip {
			color: $base-color;
			margin-left: 8upx;
		}

		.price {}

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 280upx;
			height: 100%;
			color: #fff;
			font-size: 32upx;
			background-color: $uni-color-primary;
		}
	}

	/* 消费券面板 */
	.mask {
		display: flex;
		align-items: flex-end;
		position: fixed;
		left: 0;
		top: var(--window-top);
		bottom: 0;
		width: 100%;
		background: rgba(0, 0, 0, 0);
		z-index: 9995;
		transition: 0.3s;

		.mask-content {
			width: 100%;
			min-height: 30vh;
			max-height: 70vh;
			background: #f3f3f3;
			transform: translateY(100%);
			transition: 0.3s;
			overflow-y: scroll;
		}

		&.none {
			display: none;
		}

		&.show {
			background: rgba(0, 0, 0, 0.4);

			.mask-content {
				transform: translateY(0);
			}
		}
	}

	/* 消费券列表 */
	.coupon-item {
		display: flex;
		flex-direction: column;
		margin: 20upx 24upx;
		background: #fff;

		.con {
			display: flex;
			align-items: center;
			position: relative;
			height: 120upx;
			padding: 0 30upx;

			&:after {
				position: absolute;
				left: 0;
				bottom: 0;
				content: '';
				width: 100%;
				height: 0;
				border-bottom: 1px dashed #f3f3f3;
				transform: scaleY(50%);
			}
		}

		.left {
			display: flex;
			flex-direction: column;
			justify-content: center;
			flex: 1;
			overflow: hidden;
			height: 100upx;
		}

		.title {
			font-size: 32upx;
			color: $font-color-dark;
			margin-bottom: 10upx;
		}

		.time {
			font-size: 24upx;
			color: $font-color-light;
		}

		.right {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			font-size: 26upx;
			color: $font-color-base;
			height: 100upx;
		}

		.price {
			font-size: 44upx;
			color: $base-color;

			&:before {
				content: '￥';
				font-size: 34upx;
			}
		}

		.tips {
			font-size: 24upx;
			color: $font-color-light;
			line-height: 60upx;
			padding-left: 30upx;
		}

		.circle {
			position: absolute;
			left: -6upx;
			bottom: -10upx;
			z-index: 10;
			width: 20upx;
			height: 20upx;
			background: #f3f3f3;
			border-radius: 100px;

			&.r {
				left: auto;
				right: -6upx;
			}
		}
	}
</style>