
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/index/index","pages/talentList/talentList","pages/publish/publish","pages/login/login","pages/mine/mine","pages/mall/mall","pages/cart/cart","pages/product/list","pages/product/product","pages/order/order","pages/order/createOrder","pages/order/collect","pages/order/logistics","pages/product/search","pages/category/category","pages/house/house","pages/fitment/list","pages/fitment/product","pages/fitment/search","pages/money/money","pages/money/pay","pages/money/paySuccess","pages/notice/notice","pages/notice/noticeDetail","pages/store/goodsDetail","pages/store/goodsList","pages/store/storeManagement","pages/store/storeOrder","pages/user/aboutUs","pages/user/contactUs","pages/user/friend","pages/user/level","pages/user/realName","pages/user/share","pages/user/user","pages/userMall/createOrder","pages/userMall/detail","pages/userMall/goodsList","pages/userMall/order","pages/userMall/powerAudit","pages/userMall/userMall","pages/user/capital/balance","pages/user/capital/cyBerAlloy","pages/user/capital/kindList","pages/user/capital/pond","pages/user/capital/recharge","pages/user/capital/withdrawal","pages/user/capital/withdrawalRecord"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-app","navigationBarBackgroundColor":"#F8F8F8","backgroundColor":"#F8F8F8"},"tabBar":{"color":"#CFCFCF","selectedColor":"#8147FF","borderStyle":"black","backgroundColor":"#F8F8F8","midButton":{"iconPath":"static/tabBar/<EMAIL>","width":"70px","height":"50px","iconWidth":"50px"},"list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"},{"pagePath":"pages/talentList/talentList","text":"论坛","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"},{"pagePath":"pages/mall/mall","text":"商城","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"},{"pagePath":"pages/mine/mine","text":"我的","iconPath":"static/tabBar/<EMAIL>","selectedIconPath":"static/tabBar/<EMAIL>"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"weex","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":false,"autoclose":true},"appname":"铂时","compilerVersion":"4.75","entryPagePath":"pages/index/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"首页","navigationStyle":"custom"}},{"path":"/pages/talentList/talentList","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"达人列表","navigationStyle":"custom"}},{"path":"/pages/publish/publish","meta":{},"window":{"navigationBarTitleText":"发布","navigationStyle":"custom"}},{"path":"/pages/login/login","meta":{},"window":{"navigationBarTitleText":"登录","navigationStyle":"custom"}},{"path":"/pages/mine/mine","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"我的","navigationStyle":"custom"}},{"path":"/pages/mall/mall","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"商城","navigationStyle":"custom"}},{"path":"/pages/cart/cart","meta":{},"window":{"navigationBarTitleText":"购物车"}},{"path":"/pages/product/list","meta":{},"window":{"navigationBarTitleText":"自营商城"}},{"path":"/pages/product/product","meta":{},"window":{"navigationBarTitleText":"详情展示"}},{"path":"/pages/order/order","meta":{},"window":{"navigationBarTitleText":"我的订单"}},{"path":"/pages/order/createOrder","meta":{},"window":{"navigationBarTitleText":"创建订单"}},{"path":"/pages/order/collect","meta":{},"window":{"navigationBarTitleText":"我的收藏"}},{"path":"/pages/order/logistics","meta":{},"window":{"navigationBarTitleText":"物流跟踪"}},{"path":"/pages/product/search","meta":{},"window":{"navigationBarTitleText":"商品搜索"}},{"path":"/pages/category/category","meta":{},"window":{"navigationBarTitleText":"商品分类"}},{"path":"/pages/house/house","meta":{},"window":{"navigationBarTitleText":"房屋装修"}},{"path":"/pages/fitment/list","meta":{},"window":{"navigationBarTitleText":"装修列表"}},{"path":"/pages/fitment/product","meta":{},"window":{"navigationBarTitleText":"装修商品"}},{"path":"/pages/fitment/search","meta":{},"window":{"navigationBarTitleText":"装修搜索"}},{"path":"/pages/money/money","meta":{},"window":{"navigationBarTitleText":"支付"}},{"path":"/pages/money/pay","meta":{},"window":{"navigationBarTitleText":"支付处理"}},{"path":"/pages/money/paySuccess","meta":{},"window":{"navigationBarTitleText":"支付成功"}},{"path":"/pages/notice/notice","meta":{},"window":{"navigationBarTitleText":"系统通知"}},{"path":"/pages/notice/noticeDetail","meta":{},"window":{"navigationBarTitleText":"通知详情"}},{"path":"/pages/store/goodsDetail","meta":{},"window":{"navigationBarTitleText":"店铺商品详情"}},{"path":"/pages/store/goodsList","meta":{},"window":{"navigationBarTitleText":"店铺商品列表"}},{"path":"/pages/store/storeManagement","meta":{},"window":{"navigationBarTitleText":"店铺管理"}},{"path":"/pages/store/storeOrder","meta":{},"window":{"navigationBarTitleText":"店铺订单"}},{"path":"/pages/user/aboutUs","meta":{},"window":{"navigationBarTitleText":"关于我们"}},{"path":"/pages/user/contactUs","meta":{},"window":{"navigationBarTitleText":"联系我们"}},{"path":"/pages/user/friend","meta":{},"window":{"navigationBarTitleText":"我的好友"}},{"path":"/pages/user/level","meta":{},"window":{"navigationBarTitleText":"用户等级"}},{"path":"/pages/user/realName","meta":{},"window":{"navigationBarTitleText":"实名认证"}},{"path":"/pages/user/share","meta":{},"window":{"navigationBarTitleText":"分享"}},{"path":"/pages/user/user","meta":{},"window":{"navigationBarTitleText":"个人中心"}},{"path":"/pages/userMall/createOrder","meta":{},"window":{"navigationBarTitleText":"用户商城订单"}},{"path":"/pages/userMall/detail","meta":{},"window":{"navigationBarTitleText":"用户商城详情"}},{"path":"/pages/userMall/goodsList","meta":{},"window":{"navigationBarTitleText":"用户商城商品"}},{"path":"/pages/userMall/order","meta":{},"window":{"navigationBarTitleText":"用户商城订单"}},{"path":"/pages/userMall/powerAudit","meta":{},"window":{"navigationBarTitleText":"权限审核"}},{"path":"/pages/userMall/userMall","meta":{},"window":{"navigationBarTitleText":"用户商城"}},{"path":"/pages/user/capital/balance","meta":{},"window":{"navigationBarTitleText":"余额管理"}},{"path":"/pages/user/capital/cyBerAlloy","meta":{},"window":{"navigationBarTitleText":"网络合金"}},{"path":"/pages/user/capital/kindList","meta":{},"window":{"navigationBarTitleText":"种类列表"}},{"path":"/pages/user/capital/pond","meta":{},"window":{"navigationBarTitleText":"资金池"}},{"path":"/pages/user/capital/recharge","meta":{},"window":{"navigationBarTitleText":"充值"}},{"path":"/pages/user/capital/withdrawal","meta":{},"window":{"navigationBarTitleText":"提现"}},{"path":"/pages/user/capital/withdrawalRecord","meta":{},"window":{"navigationBarTitleText":"提现记录"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
