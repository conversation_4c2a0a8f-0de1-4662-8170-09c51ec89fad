import Vue from 'vue'
import Vuex from 'vuex'
//数据持久化插件 - 使用uni-app本地存储实现
// import createPersistedState from 'vuex-persistedstate'
Vue.use(Vuex)

// 简化版数据持久化 - 直接使用uni-app存储API
const vuexPersisted = (store) => {
	// 从本地存储恢复状态
	try {
		const savedState = uni.getStorageSync('vuex-state');
		if (savedState) {
			store.replaceState(Object.assign(store.state, JSON.parse(savedState)));
		}
	} catch (e) {
		console.error('恢复状态失败:', e);
	}

	// 监听状态变化并保存到本地存储
	store.subscribe((mutation, state) => {
		try {
			uni.setStorageSync('vuex-state', JSON.stringify(state));
		} catch (e) {
			console.error('保存状态失败:', e);
		}
	});
};
const store = new Vuex.Store({
	state: {
		userInfo: {},
		detail: [],
		order: {},
		isLogin: false,
		joinPicUrl: "https://boshi.channce.com/imgs/"
	},
	mutations: {
		login(state, provider) {
			state.userInfo = provider.loginSubscribersVO;
			state.isLogin = true
			uni.setStorage({ //缓存用户登陆状态
				key: 'userInfo',
				data: provider.loginSubscribersVO
			})
			uni.setStorage({ //缓存用户登陆状态
				key: 'isLogin',
				data: true
			})
		},
		logout(state) {
			state.userInfo = {};
			uni.clearStorage();
		},
		delDetail(state) {
			state.detail = []
		},
		getDetail(state, provider) {
			console.log(state.detail, provider);
			state.detail.push(provider);
		},
		addOrder(state, provider) {
			state.order = provider;
		},
		delOrder() {
			state.order = {}
		}
	},
	actions: {

	},
	plugins: [vuexPersisted]
})

export default store
