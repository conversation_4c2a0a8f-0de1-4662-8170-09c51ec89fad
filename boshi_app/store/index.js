import Vue from 'vue'
import Vuex from 'vuex'
//数据持久化插件
// import createPersistedState from 'vuex-persistedstate'
Vue.use(Vuex)

// const vuexPersisted = new createPersistedState({
// 	storage: {
// 		getItem: key => uni.getStorageSync(key),
// 		setItem: (key, value) => uni.setStorageSync(key, value),
// 		removeItem: key => uni.removeStorageSync(key)
// 	}
// })
const store = new Vuex.Store({
	state: {
		userInfo: {},
		detail: [],
		order: {},
		isLogin: false,
		joinPicUrl: "https://boshi.channce.com/imgs/"
	},
	mutations: {
		login(state, provider) {
			state.userInfo = provider.loginSubscribersVO;
			state.isLogin = true
			uni.setStorage({ //缓存用户登陆状态
				key: 'userInfo',
				data: provider.loginSubscribersVO
			})
			uni.setStorage({ //缓存用户登陆状态
				key: 'isLogin',
				data: true
			})
		},
		logout(state) {
			state.userInfo = {};
			uni.clearStorage();
		},
		delDetail(state) {
			state.detail = []
		},
		getDetail(state, provider) {
			console.log(state.detail, provider);
			state.detail.push(provider);
		},
		addOrder(state, provider) {
			state.order = provider;
		},
		delOrder() {
			state.order = {}
		}
	},
	actions: {

	},
	// plugins: [vuexPersisted]
})

export default store
