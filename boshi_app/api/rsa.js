// import CryptoJS from 'crypto-js/crypto-js'
export default {
	encrypt(data, is) {
		console.log(data);
		if (!is) {
			return JSON.stringify(data)
		}
		// 暂时返回原始数据，等待crypto-js依赖安装
		return JSON.stringify(data);

		// 原始加密逻辑（需要crypto-js）
		// let srcs = CryptoJS.enc.Utf8.parse(JSON.stringify(data));
		// var key = CryptoJS.enc.Utf8.parse(uni.getStorageSync('token1'))
		// var iv = CryptoJS.enc.Utf8.parse(uni.getStorageSync('token2'))
		// var encrypted = CryptoJS.AES.encrypt(srcs, key, {
		// 	iv: iv,
		// 	mode: CryptoJS.mode.CBC,
		// 	padding: CryptoJS.pad.ZeroPadding
		// });
		// return CryptoJS.enc.Hex.stringify(encrypted.ciphertext);
	}
}
