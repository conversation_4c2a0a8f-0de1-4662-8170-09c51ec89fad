{"_from": "base-64", "_id": "base-64@1.0.0", "_inBundle": false, "_integrity": "sha512-kwDPIFCGx0NZHog36dj+tHiwP4QMzsZ3AgMViUBKI0+V5n4U0ufTCUMhnQ04diaRI8EX/QcPfql7zlhZ7j4zgg==", "_location": "/base-64", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "base-64", "name": "base-64", "escapedName": "base-64", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/base-64/-/base-64-1.0.0.tgz", "_shasum": "09d0f2084e32a3fd08c2475b973788eee6ae8f4a", "_spec": "base-64", "_where": "D:\\项目\\老项目\\悦享生活\\悦享生活", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/base64/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A robust base64 encoder/decoder that is fully compatible with `atob()` and `btoa()`, written in JavaScript.", "devDependencies": {"coveralls": "^2.11.4", "grunt": "^0.4.5", "grunt-cli": "^1.3.2", "grunt-shell": "^1.1.2", "grunt-template": "^0.2.3", "istanbul": "^0.4.0", "mocha": "^6.2.0", "regenerate": "^1.2.1"}, "files": ["LICENSE-MIT.txt", "base64.js"], "homepage": "https://mths.be/base64", "keywords": ["codec", "decoder", "encoder", "base64", "atob", "btoa"], "license": "MIT", "main": "base64.js", "name": "base-64", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/base64.git"}, "scripts": {"build": "grunt build", "test": "mocha tests/tests.js"}, "version": "1.0.0"}