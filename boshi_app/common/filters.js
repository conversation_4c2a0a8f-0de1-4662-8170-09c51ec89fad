function jointPic(value) {
	if (value) {
		return `https://boshi.channce.com/imgs/${value}`;
	}
}

function formatDate(time) {
	let date = new Date(time);
	return date.toLocaleString();
}

function retainTailNumber(value) {
	if (value.length > 4) {
		return value.substr(value.length - 4);
	} else {
		return value
	}
}

// 超出文字中间显示省略符
function ellipsis(value) {
	let len = value.length;
	if (!value) return "";
	if (value.length > 10) {
		return value.substring(0, 3) + "..." + value.substring(len - 4, len);
	}
	return value;
}

function number_format(number, decimals, dec_point, thousands_sep) {
	decimals = 2; //这里默认设置保留两位小数
	number = (number + '').replace(/[^0-9+-Ee.]/g, '');
	var n = !isFinite(+number) ? 0 : +number,
		prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
		sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
		dec = (typeof dec_point === 'undefined') ? '.' : dec_point;
	var s = n.toString().split('.');
	var re = /(-?\d+)(\d{3})/;
	while (re.test(s[0])) {
		s[0] = s[0].replace(re, "$1" + sep + "$2");
	}
	if ((s[1] || '').length < prec) {
		s[1] = s[1] || '';
		s[1] += new Array(prec - s[1].length + 1).join('0');
	} else {
		s[1] = s[1].substring(0, prec);
	}
	return s.join(dec);
}

export default {
	jointPic,
	formatDate,
	retainTailNumber,
	ellipsis,
	number_format
}
