// #ifndef VUE3
import Vue from "vue";
import App from "./App";
import store from "./store";
import http from "./api/http.js";
import rsa from "@/api/rsa.js";
import filters from "./common/filters.js";

Vue.config.productionTip = false;

// 添加全局filter
Object.keys(filters).map((v) => {
  Vue.filter(v, filters[v]);
});

const msg = (title, duration = 4000, mask = false, icon = "none") => {
  //统一提示方便全局修改
  if (Boolean(title) === false) {
    return;
  }
  uni.showToast({
    title,
    duration,
    mask,
    icon,
  });
};

const json = (type) => {
  //模拟异步请求数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(Json[type]);
    }, 500);
  });
};

const prePage = () => {
  let pages = getCurrentPages();
  let prePage = pages[pages.length - 2];
  // #ifdef H5
  return prePage;
  // #endif
  return prePage.$vm;
};

Vue.prototype.$rsa = rsa;
Vue.prototype.$store = store;
Vue.prototype.$api = { msg, json, prePage };
Vue.prototype.http = http;

App.mpType = "app";

const app = new Vue({
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";
import App from "./App.vue";
export function createApp() {
  const app = createSSRApp(App);
  return {
    app,
  };
}
// #endif
