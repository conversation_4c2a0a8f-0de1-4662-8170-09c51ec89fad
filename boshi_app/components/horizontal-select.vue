<template>
	<scroll-view scroll-x class="select" :show-scrollbar='false'>
		<view v-if="selectOption.type == 'text' || selectOption.type == null" class="select-item"
			v-for="(item,index) in selectOption.selectList" :class="{active: item.id === checkedId}"
			:style="{'--textColor':selectOption.textColor}" @click="eventClick(item)">
			{{item.title}}
		</view>
		<view v-if="selectOption.type == 'button'" class="select-item button"
			v-for="(item,index) in selectOption.selectList" :class="{actives: item.id === checkedId}"
			:style="{'--bgColor':selectOption.bgColor,'--textColor':selectOption.textColor,'--fontSize':selectOption.fontSize}"
			@click="eventClick(item)">
			{{item.title}}
		</view>
	</scroll-view>
</template>

<script>
	export default {
		name: "horizontal-select",
		data() {
			return {
				checkedId: this.selectOption.selectId
			};
		},
		created() {
			console.log(this.selectOption.selectId);
		},
		props: {
			selectOption: {
				type: Object,
				default: {
					// 列表数据
					selectList: [],
					// 选中ID
					selectId: 1,
					// 类型 text button
					type: "text",
					//背景颜色 （button模式下生效）
					bgColor: "#ff0000",
					// 文字颜色
					textColor: "#ff0000",
					// 文字大小
					fontSize: '13px'
				}
			}
		},
		methods: {
			eventClick(item) {
				this.checkedId = item.id
				this.$emit('eventClick', item.id);
			}
		}
	}
</script>

<style lang="scss">
	.select {
		white-space: nowrap;
		width: 100%;

		.select-item {
			display: inline-block;
			text-align: center;
			font-size: var(--fontSize);
			height: 70rpx;
			padding: 0 10px;
		}

		.button {
			display: inline-flex;
			align-items: center;
			justify-content: center;
		}

		.active {
			color: var(--textColor);
			position: relative;
			font-weight: bold;
		}

		.active:after {
			content: '';
			width: 70%;
			height: 4px;
			border-radius: 50px;
			background-color: var(--textColor);
			position: absolute;
			bottom: 5px;
			left: 50%;
			transform: translate(-50%);
		}

		.actives {
			background-color: var(--bgColor);
			color: var(--textColor);
			border-radius: 3px;
		}
	}
</style>