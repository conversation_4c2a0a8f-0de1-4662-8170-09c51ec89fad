<template>
	<view>
		<view class="goods-info">
			<view class="goods-imgs">
				<image :src="info.img"></image>
			</view>
			<view class="goods-title">{{info.title}}</view>
			<view class="goods-boom">
				<view class="goods-re">
					￥<text>279.0</text>
				</view>
				<viwe class="nums">482人付款</viwe>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"goodsCard",
		props: {
			info: {
				type: Object,
				default: function() {
					return {}
				}
			}
		},
		data() {
			return {
				
			};
		},
		methods:{
			getDatails(){
				uni.navigateTo({
					url:'/pages/details/details?id='+this.info.id
				})
			},
		},
	}
</script>

<style lang="less">
.goods-info{
	border-radius: 20rpx;
	background-color: #fff;
	margin: 10rpx 10rpx 15rpx 10rpx;
}
.goods-imgs{
	width: 100%;
	height: 340rpx;
	position: relative;
	image{
		width: 342rpx;
		height: 340rpx;
		// border-radius: 20rpx 20rpx 0rpx 0rpx;
	}
}
.goods-title{
	padding: 9rpx 18rpx;
	color: #323332;
	font-size: 28rpx;
}
.goods-boom{
	padding: 15rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.goods-re{
		color: #FB2B1C;
		font-size: 24rpx;
		text{
			font-size: 34rpx;
			font-weight: 600;
		}
	}
	.nums{
		color: #999999;
		font-size: 22rpx;
	}
}
</style>
